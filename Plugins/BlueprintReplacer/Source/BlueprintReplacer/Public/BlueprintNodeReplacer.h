// BlueprintNodeReplacer.h

#pragma once

#include "CoreMinimal.h"
#include "Kismet2/BlueprintEditorUtils.h"

class FBlueprintNodeReplacer
{
public:
    static void ReplaceStartCameraFadeWithFadeOut(UBlueprint* Blueprint,int& NodeCount);
	static bool IsNodeDisconnected(UEdGraphNode* Node);
    private:
   
	static void ReplaceWithFadeOut(UBlueprint* Blueprint,UEdGraph* ParentGraph,UK2Node_CallFunction* OldNode,float Duration);
	static void ReplaceWithFadeIn(UBlueprint* Blueprint,UEdGraph* ParentGraph,UK2Node_CallFunction* OldNode,float Duration);
	static void ReplaceWithReadyForFade(UBlueprint* Blueprint,UEdGraph* ParentGraph,UK2Node_CallFunction* OldNode,float Duration);
	static UBlueprint* GetLib(TCHAR* LibPath);
	static UFunction* GetLibFunction(TCH<PERSON>* <PERSON>b<PERSON>ath,FName FunctionName);
};

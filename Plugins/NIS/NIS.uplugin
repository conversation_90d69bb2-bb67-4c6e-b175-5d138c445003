{"FileVersion": 3, "Version": 4, "VersionName": "1.2.1", "FriendlyName": "NVIDIA NIS", "Description": "NVIDIA Image Scaling", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/image-scaling", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.3.0", "CanContainContent": false, "Installed": true, "Modules": [{"Name": "NISCore", "Type": "Runtime", "LoadingPhase": "PostEngineInit"}, {"Name": "NISShaders", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "NISBlueprint", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}]}
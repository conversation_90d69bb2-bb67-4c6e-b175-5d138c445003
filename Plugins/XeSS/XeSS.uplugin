{"FileVersion": 3, "Version": 1, "VersionName": "1.3.3", "FriendlyName": "Intel® XeSS", "Description": "Intel® Xe Super Sampling (XeSS) technology uses machine learning to deliver more performance with exceptional image quality.", "Category": "Rendering", "CreatedBy": "Intel Corporation", "CreatedByURL": "", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EngineVersion": "5.3.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "XeSSPrePass", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "XeSSPlugin", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "XeSSBlueprint", "Type": "Runtime", "LoadingPhase": "PostEngineInit"}], "LocalizationTargets": [{"Name": "XeSS", "LoadingPolicy": "Always"}]}
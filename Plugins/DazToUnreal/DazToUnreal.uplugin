{"FileVersion": 3, "Version": 2023, "VersionName": "2023.v1.2.63", "FriendlyName": "DazToUnreal", "Description": "", "Category": "Other", "CreatedBy": "Daz 3D, Inc", "CreatedByURL": "", "DocsURL": "https://github.com/daz3d/DazToUnreal#readme", "MarketplaceURL": "", "SupportURL": "https://github.com/daz3d/DazToUnreal/issues", "EngineVersion": "5.3.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "DazToUnreal", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "<PERSON>"]}], "Plugins": [{"Name": "EditorScriptingUtilities", "Enabled": true}, {"Name": "LiveLink", "Enabled": true}, {"Name": "IKRig", "Enabled": true, "Optional": true}, {"Name": "AlembicImporter", "Enabled": true, "Optional": true}, {"Name": "MLDeformerFramework", "Enabled": true, "Optional": true}, {"Name": "NNI", "Enabled": true, "Optional": true}, {"Name": "NeuralMorphModel", "Enabled": true, "Optional": true}, {"Name": "VertexDeltaModel", "Enabled": true, "Optional": true}, {"Name": "ControlRig", "Enabled": true, "Optional": true}]}
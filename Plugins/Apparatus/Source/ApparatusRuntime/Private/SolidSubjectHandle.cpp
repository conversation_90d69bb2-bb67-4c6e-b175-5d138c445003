/*
 * ░▒▓ APPARATUS ▓▒░
 * 
 * File: SolidSubjectHandle.cpp
 * Created: 2021-07-24 18:37:31
 * Author: <PERSON><PERSON> (v<PERSON><PERSON>@turbanov.ru)
 * ───────────────────────────────────────────────────────────────────
 * 
 * The Apparatus source code is for your internal usage only.
 * Redistribution of this file is strictly prohibited.
 * 
 * Community forums: https://talk.turbanov.ru
 * 
 * Copyright 2019 - 2023, SP <PERSON><PERSON>
 * Made in Russia, Moscow City, Chekhov City ♡
 */

#include "SolidSubjectHandle.h"

const FSolidSubjectHandle FSolidSubjectHandle::Invalid;

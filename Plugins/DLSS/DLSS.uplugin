{"FileVersion": 3, "Version": 37, "VersionName": "3.5.2b", "FriendlyName": "NVIDIA DLSS Super Resolution/Ray Reconstruction/DLAA", "Description": "NVIDIA Deep Learning Super Sampling Super Resolution and Ray Reconstruction", "Category": "Rendering", "CreatedBy": "NVIDIA", "CreatedByURL": "https://developer.nvidia.com/dlss", "DocsURL": "", "MarketplaceURL": "https://www.unrealengine.com/marketplace/en-US/product/nvidia-dlss", "SupportURL": "mailto:<EMAIL>", "EngineVersion": "5.3.0", "CanContainContent": true, "Installed": true, "Modules": [{"Name": "DLSSUtility", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "DLSS", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "DLSSBlueprint", "Type": "Runtime", "LoadingPhase": "PostConfigInit"}, {"Name": "NGXRHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXD3D11RHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXD3D12RHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXVulkanRHIPreInit", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}, {"Name": "NGXVulkanRHI", "Type": "Runtime", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}, {"Name": "DLSSEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit", "PlatformAllowList": ["Win64"]}]}
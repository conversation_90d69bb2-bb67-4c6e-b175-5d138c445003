// Copyright 2024 JBernardic. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"

#include "Input/Reply.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Widgets/SCompoundWidget.h"
#include "Engine/Texture2D.h"
#include "IDetailsView.h"


#include "Layout/Visibility.h"
#include "Widgets/SWidget.h"

#include "IDetailCustomization.h"

#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Input/Reply.h"
#include "Widgets/SWindow.h"

#include "ContentBrowserDelegates.h"

class UTextureRenderTarget2D;

class FSkeletalMeshLODRenderData;
class FSkinWeightVertexBuffer;
struct FActiveMorphTarget;
class UVertexAnimProfile;

class VERTEXANIMATIONTOOLKIT_API FVertexAnimationUtils
{
public:

	/**
	 * Convert a set of mesh components in their current pose to a static mesh.
	 * @param	InMeshComponents		The mesh components we want to convert
	 * @param	InRootTransform			The transform of the root of the mesh we want to output
	 * @param	InPackageName			The package name to create the static mesh in. If this is empty then a dialog will be displayed to pick the mesh.
	 * @return a new static mesh (specified by the user)
	 */
	static UStaticMesh* ConvertMeshesToStaticMesh(const int AdditionalTextureIndexOffset, const TArray<UMeshComponent*>& InMeshComponents, const FTransform& InRootTransform = FTransform::Identity, const FString& InPackageName = FString());
	static void PushNotification(FString text, UObject* objectToLink);

};
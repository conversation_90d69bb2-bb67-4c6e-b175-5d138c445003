// Copyright 2024 JBernardic. All Rights Reserved.
#include <Editor/MaterialEditor/Private/MaterialEditor.h>
#include <MaterialGraph/MaterialGraph.h>
#include <MaterialEditingLibrary.h>
#include <Materials/MaterialExpressionComment.h>

bool FMaterialEditor::UpdateOriginalMaterial()
{
	// Handle propagation of the material function being edited
	if (MaterialFunction)
	{
		// Copy the expressions back from the preview material
		MaterialFunction->AssignExpressionCollection(Material->GetExpressionCollection());

		// Preserve the thumbnail info
		UThumbnailInfo* OriginalThumbnailInfo = MaterialFunction->ParentFunction->ThumbnailInfo;
		UThumbnailInfo* ThumbnailInfo = MaterialFunction->ThumbnailInfo;
		MaterialFunction->ParentFunction->ThumbnailInfo = NULL;
		MaterialFunction->ThumbnailInfo = NULL;

		// Cache any metadata
		const TMap<FName, FString>* MetaData = UMetaData::GetMapForObject(MaterialFunction->ParentFunction);

		// overwrite the original material function in place by constructing a new one with the same name
		MaterialFunction->ParentFunction = (UMaterialFunction*)StaticDuplicateObject(
			MaterialFunction,
			MaterialFunction->ParentFunction->GetOuter(),
			MaterialFunction->ParentFunction->GetFName(),
			RF_AllFlags,
			MaterialFunction->ParentFunction->GetClass());

		// Restore the thumbnail info
		MaterialFunction->ParentFunction->ThumbnailInfo = OriginalThumbnailInfo;
		MaterialFunction->ThumbnailInfo = ThumbnailInfo;

		// Restore the metadata
		if (MetaData)
		{
			UMetaData* PackageMetaData = MaterialFunction->ParentFunction->GetOutermost()->GetMetaData();
			PackageMetaData->SetObjectValues(MaterialFunction->ParentFunction, *MetaData);
		}

		// Restore RF_Standalone on the original material function, as it had been removed from the preview material so that it could be GC'd.
		MaterialFunction->ParentFunction->SetFlags(RF_Standalone);

		for (UMaterialExpression* CurrentExpression : MaterialFunction->ParentFunction->GetExpressions())
		{
			ensureMsgf(CurrentExpression, TEXT("Invalid expression whilst saving material function."));

			// Link the expressions back to their function
			if (CurrentExpression)
			{
				CurrentExpression->Material = NULL;
				CurrentExpression->Function = MaterialFunction->ParentFunction;
			}
		}
		for (UMaterialExpressionComment* CurrentExpression : MaterialFunction->ParentFunction->GetEditorComments())
		{
			ensureMsgf(CurrentExpression, TEXT("Invalid comment whilst saving material function."));

			// Link the expressions back to their function
			if (CurrentExpression)
			{
				CurrentExpression->Material = NULL;
				CurrentExpression->Function = MaterialFunction->ParentFunction;
			}
		}

		// clear the dirty flag
		bMaterialDirty = false;
		bStatsFromPreviewMaterial = false;

		UMaterialEditingLibrary::UpdateMaterialFunction(MaterialFunction->ParentFunction, Material);
	}
	return true;
}
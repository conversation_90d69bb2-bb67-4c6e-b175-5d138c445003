// Copyright 2024 JBernardic. All Rights Reserved.

#include "VertexAnimationToolkitStyle.h"
#include "VertexAnimationToolkit.h"
#include "Framework/Application/SlateApplication.h"
#include "Styling/SlateStyleRegistry.h"
#include "Slate/SlateGameResources.h"
#include "Interfaces/IPluginManager.h"
#include "Styling/SlateStyleMacros.h"

#define RootToContentDir Style->RootToContentDir

TSharedPtr<FSlateStyleSet> FVertexAnimationToolkitStyle::StyleInstance = nullptr;

void FVertexAnimationToolkitStyle::Initialize()
{
	if (!StyleInstance.IsValid())
	{
		StyleInstance = Create();
		FSlateStyleRegistry::RegisterSlateStyle(*StyleInstance);
	}
}

void FVertexAnimationToolkitStyle::Shutdown()
{
	FSlateStyleRegistry::UnRegisterSlateStyle(*StyleInstance);
	ensure(StyleInstance.IsUnique());
	StyleInstance.Reset();
}

FName FVertexAnimationToolkitStyle::GetStyleSetName()
{
	static FName StyleSetName(TEXT("VertexAnimationToolkitStyle"));
	return StyleSetName;
}


const FVector2D Icon16x16(16.0f, 16.0f);
const FVector2D Icon20x20(20.0f, 20.0f);

TSharedRef< FSlateStyleSet > FVertexAnimationToolkitStyle::Create()
{
	TSharedRef< FSlateStyleSet > Style = MakeShareable(new FSlateStyleSet("VertexAnimationToolkitStyle"));
	Style->SetContentRoot(IPluginManager::Get().FindPlugin("VertexAnimationToolkit")->GetBaseDir() / TEXT("Resources"));

	Style->Set("VertexAnimationToolkit.PluginAction", new IMAGE_BRUSH_SVG(TEXT("PlaceholderButtonIcon"), Icon20x20));
	return Style;
}

void FVertexAnimationToolkitStyle::ReloadTextures()
{
	if (FSlateApplication::IsInitialized())
	{
		FSlateApplication::Get().GetRenderer()->ReloadTextureResources();
	}
}

const ISlateStyle& FVertexAnimationToolkitStyle::Get()
{
	return *StyleInstance;
}

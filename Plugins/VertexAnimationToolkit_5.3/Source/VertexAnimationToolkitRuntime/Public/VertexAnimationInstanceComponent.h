// Copyright 2024 JBernardic. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneComponent.h"
#include "VertexAnimationSubsystem.h"
#include "VertexAnimationInstanceComponent.generated.h"


UCLASS( ClassGroup=(VertexAnimationToolkit), meta=(BlueprintSpawnableComponent) )
class VERTEXANIMATIONTOOLKITRUNTIME_API UVertexAnimationInstanceComponent : public USceneComponent
{
	GENERATED_BODY()

public:	
	// Sets default values for this component's properties
	UVertexAnimationInstanceComponent();

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Vertex Animation Instance")
	UStaticMesh* StaticMesh;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Vertex Animation Instance")
	TArray<float> InitialCustomData;

	UFUNCTION(BlueprintCallable, Category = "Vertex Animation Instance")
	void SetStaticMesh(UStaticMesh* NewStaticMesh);

	UFUNCTION(BlueprintCallable, Category = "Vertex Animation Instance")
	void SetCustomData(int CustomDataIndex, float Value);

	UFUNCTION(BlueprintCallable, Category = "Vertex Animation Instance")
	int GetInstanceId() const;

protected:
	// Called when the game starts
	virtual void BeginPlay() override;
	virtual void OnUnregister() override;

public:	
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
private:
	UVertexAnimationSubsystem* VertexAnimationSubsystem;
	int InstanceId = -1;

		
};

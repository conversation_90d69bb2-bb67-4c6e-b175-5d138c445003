// Copyright 2024 JBernardic. All Rights Reserved.


#include "VertexAnimationHISM.h"

bool UVertexAnimationHISM::BatchUpdateInstancesTransforms(int32 StartInstanceIndex, const TArray<FTransform>& NewInstancesTransforms, bool bWorldSpace = false, bool bMarkRenderStateDirty = false, bool bTeleport = false)
{
#if (ENGINE_MAJOR_VERSION == 5) && (ENGINE_MINOR_VERSION >= 4)
	UpdatePrevTransforms(bWorldSpace);

	PrevTransforms.SetNum(PerInstanceSMData.Num());
	for (int i = StartInstanceIndex; i < NewInstancesTransforms.Num(); ++i) {
		PrevTransforms[i] = NewInstancesTransforms[i - StartInstanceIndex];
	}
#endif

	return UInstancedStaticMeshComponent::BatchUpdateInstancesTransforms(StartInstanceIndex, NewInstancesTransforms, bWorldSpace, bMarkRenderStateDirty, bTeleport);
}

bool UVertexAnimationHISM::BatchUpdateInstancesTransform(int32 StartInstanceIndex, int32 NumInstances, const FTransform& NewInstancesTransform, bool bWorldSpace, bool bMarkRenderStateDirty, bool bTeleport)
{
#if (ENGINE_MAJOR_VERSION == 5) && (ENGINE_MINOR_VERSION >= 4)
	UpdatePrevTransforms(bWorldSpace);

	PrevTransforms.SetNum(PerInstanceSMData.Num());
	for (int i = StartInstanceIndex; i < NumInstances; ++i) {
		PrevTransforms[i] = NewInstancesTransform;
	}
#endif

	return UInstancedStaticMeshComponent::BatchUpdateInstancesTransform(StartInstanceIndex, NumInstances, NewInstancesTransform, bWorldSpace, bMarkRenderStateDirty, bTeleport);
}

bool UVertexAnimationHISM::UpdateInstanceTransform(int32 InstanceIndex, const FTransform& NewInstanceTransform, bool bWorldSpace, bool bMarkRenderStateDirty, bool bTeleport)
{
#if (ENGINE_MAJOR_VERSION == 5) && (ENGINE_MINOR_VERSION >= 4)
	if (!PrevTransforms.IsEmpty()) {
		PerInstancePrevTransform.SetNumUninitialized(PrevTransforms.Num());
		SetHasPerInstancePrevTransforms(true);
		FTransform NewPrevInstanceTransform = PrevTransforms[InstanceIndex];
		FTransform LocalPrevTransform = bWorldSpace ? NewPrevInstanceTransform.GetRelativeTransform(GetComponentTransform()) : NewPrevInstanceTransform;
		PerInstancePrevTransform[InstanceIndex] = LocalPrevTransform.ToMatrixWithScale();
	}

	PrevTransforms.SetNum(PerInstanceSMData.Num());
	PrevTransforms[InstanceIndex] = NewInstanceTransform;
#endif

	return UInstancedStaticMeshComponent::UpdateInstanceTransform(InstanceIndex, NewInstanceTransform, bWorldSpace, bMarkRenderStateDirty, bTeleport);
}

void UVertexAnimationHISM::UpdatePrevTransforms(bool bWorldSpace)
{
#if (ENGINE_MAJOR_VERSION == 5) && (ENGINE_MINOR_VERSION >= 4)
	PerInstancePrevTransform.SetNumUninitialized(PrevTransforms.Num());
	SetHasPerInstancePrevTransforms(true);
	for (int i = 0; i < PrevTransforms.Num(); ++i) {
		FTransform NewPrevInstanceTransform = PrevTransforms[i];
		FTransform LocalPrevTransform = bWorldSpace ? NewPrevInstanceTransform.GetRelativeTransform(GetComponentTransform()) : NewPrevInstanceTransform;
		PerInstancePrevTransform[i] = LocalPrevTransform.ToMatrixWithScale();
	}
#endif
}

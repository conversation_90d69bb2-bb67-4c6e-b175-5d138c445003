// Copyright 2024 JBernardic. All Rights Reserved.


#include "VertexAnimationInstanceComponent.h"
#include <Engine/World.h>

// Sets default values for this component's properties
UVertexAnimationInstanceComponent::UVertexAnimationInstanceComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.bStartWithTickEnabled = true;
}


void UVertexAnimationInstanceComponent::SetStaticMesh(UStaticMesh* NewStaticMesh)
{
	VertexAnimationSubsystem->RemoveInstanceById(StaticMesh, InstanceId);
	StaticMesh = NewStaticMesh;
	VertexAnimationSubsystem->AddInstance(StaticMesh, GetComponentTransform());
}

void UVertexAnimationInstanceComponent::SetCustomData(int CustomDataIndex, float Value)
{
	VertexAnimationSubsystem->SetCustomDataById(StaticMesh, InstanceId, CustomDataIndex, Value);
}

int UVertexAnimationInstanceComponent::GetInstanceId() const
{
	return InstanceId;
}

// Called when the game starts
void UVertexAnimationInstanceComponent::BeginPlay()
{
	Super::BeginPlay();

	SetComponentTickEnabled(true);

	VertexAnimationSubsystem = GetWorld()->GetSubsystem<UVertexAnimationSubsystem>();
	InstanceId = VertexAnimationSubsystem->AddInstance(StaticMesh, GetComponentTransform());

	for (int i = 0; i < InitialCustomData.Num(); ++i) {
		SetCustomData(i, InitialCustomData[i]);
	}
}

void UVertexAnimationInstanceComponent::OnUnregister()
{
	if (InstanceId >= 0) {
		VertexAnimationSubsystem->RemoveInstanceById(StaticMesh, InstanceId);
	}
	Super::OnUnregister();
}


// Called every frame
void UVertexAnimationInstanceComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	VertexAnimationSubsystem->UpdateInstanceTransformById(StaticMesh, InstanceId, GetComponentTransform());
}


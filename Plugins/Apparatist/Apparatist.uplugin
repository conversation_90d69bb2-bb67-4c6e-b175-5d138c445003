{"FileVersion": 3, "Version": 1, "VersionName": "0.2.0", "FriendlyName": "Apparatist", "Description": "Official Apparatus utility toolkit.", "Category": "Workflow", "CreatedBy": "SP <PERSON><PERSON>", "CreatedByURL": "https://turbanov.ru", "DocsURL": "https://github.com/toolworks/Apparatist", "SupportURL": "https://github.com/toolworks/Apparatist", "EnabledByDefault": true, "CanContainContent": false, "IsBetaVersion": true, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "ApparatistRuntime", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault"}, {"Name": "ApparatistEditor", "Type": "Editor", "LoadingPhase": "PostEngineInit"}], "Plugins": [{"Name": "<PERSON><PERSON><PERSON><PERSON>", "Enabled": true, "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/66d674e3675f44c1a1e619de8adcef71"}]}
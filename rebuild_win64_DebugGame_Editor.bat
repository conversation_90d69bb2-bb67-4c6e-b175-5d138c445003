@echo off

set RunUATPath="C:\Program Files\Epic Games\UE_5.3\Engine\Build\BatchFiles\"
set UProjectPath=%~dp0
@REM ProjectAwaker Win64 DebugGame
call %RunUATPath%Rebuild.bat ProjectAwakerEditor Win64 DebugGame -Project="%UProjectPath%TheAwakener_FO.uproject" -WaitMutex -FromMsBuild

if errorlevel 1 goto Error_BuildError

goto Exit

:Error_BuildError
echo ERROR: Failed to build the project
exit /B %ERRORLEVEL%

:Exit
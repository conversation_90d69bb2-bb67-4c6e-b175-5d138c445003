// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#include "ShoppingGoodsPoint.h"
#include "Trading.h"
#include "Camera/CameraComponent.h"
#include "Components/WidgetComponent.h"
#include "TheAwakener_FO/GameFramework/Input/CommandDirection.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

#include "TheAwakener_FO/UI/GameMain/ShopItem.h"
#include "TheAwakener_FO/UI/GameMain/ShopItemPrice.h"

#include "MerchantShop.generated.h"

/**
 * 商店显示的贩卖物信息
 */
USTRUCT()
struct FVendorGoodsAppearanceInfo
{
	GENERATED_BODY()
public:
	//所属第几个道具挂点
	UPROPERTY()
	int Index;

	//挂了啥
	UPROPERTY()
	AActor* Actor;

	FVendorGoodsAppearanceInfo():Index(-1), Actor(nullptr){};
	FVendorGoodsAppearanceInfo(int AtIndex, AActor* ShowingActor):
		Index(AtIndex), Actor(ShowingActor){};
};

/**
 * 一家商店
 */
UCLASS(config=Game, BlueprintType)
class THEAWAKENER_FO_API AMerchantShop : public AActor
{
	GENERATED_BODY()
private:
	//所有的物品挂点，卖的东西都挂这了
	UPROPERTY()
	TArray<UShoppingGoodsPoint*> GoodsPoints;

	//当前焦点在哪个挂点
	UPROPERTY()
	UShoppingGoodsPoint* CurSelect = nullptr;
	UPROPERTY()
	UShoppingGoodsPoint* WasSelect = nullptr;

	//是否正在交易某个东西（播放购买过程）
	UPROPERTY()
	bool BuyingThing = false;
	UPROPERTY()
	float BuyingTimeElapsed = 0;	//播放购买动画的计时器
	UPROPERTY()
	int BuyingIndex= -1;	//正在买哪个

	UPROPERTY()
	TArray<FVendorGoodsAppearanceInfo> ShowingAppearance;

	UPROPERTY()
	UCameraComponent* ShopCam;

	UShoppingGoodsPoint* GetPointByIndex(int Index);
	UShoppingGoodsPoint* NextPoint(ECommandDirection Direction);

	void ShowCurSelectDetail();
	AActor* CurSelectedActor();

	//选中购买物品的标志
	/*UPROPERTY()
	ADealSelectSign* DealSelectSign;*/
	UPROPERTY()
	bool MovingDealSelectSign = false;//正在移动光标
	UPROPERTY()
	float MovingSignTimeElapsed = 0;	//移动了多久了
	UPROPERTY()
	FVector DealSelectSignTarget;	//光标要移动到的坐标
	UPROPERTY()
	FVector DealSelectSignStartPos;	//光标开始移动的位置

	void ShowDealSelectSign(int AtIndex = 0);
	void HideDealSelectSign();
	void MoveDealSelectSignTo(int ToIndex);

	UPROPERTY()
	UShopItem* ShopItem;						//商品UI对象

	UPROPERTY()
	UShopItemPrice* ShopItemPrice;

	UPROPERTY()
	bool GoHide = false;
	
protected:
	virtual void BeginPlay() override;
	virtual void Tick(float DeltaSeconds) override;

	void ChangeCurrSelectByCmd(ECommandDirection CommandDirection);
	
public:
	AMerchantShop();
	//这家店的信息
	UPROPERTY(BlueprintReadOnly)
	FTrading ShopInfo;

	//这家商店要设置的商店Id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString ShopId;

	UFUNCTION(BlueprintCallable)
	void Set(FTrading Shop);

	//店老板是谁，如果店老板被杀死，就会所有东西免费，但是如果只是nullptr还是得花钱
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	AAwCharacter* ShopKeeper = nullptr;

	//显示可以交互的按钮
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UWidgetComponent* InteractUI;
	UFUNCTION(BlueprintCallable)
	void ShowInteractSign();
	UFUNCTION(BlueprintCallable)
	void HideInteractSign();
	UFUNCTION(BlueprintCallable)
	void OnInteract(AAwCharacter* Buyer);
	UFUNCTION(BlueprintCallable)
	void EndInteract(AAwCharacter* Buyer);
	
	
	//显示商品UI的UI组件
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UWidgetComponent* ItemWidgetComponent;

	//初始化、显示、隐藏、挂点、设置商品UI的函数
	UFUNCTION(BlueprintCallable)
	void InitShopUI();
	UFUNCTION(BlueprintCallable)
	void ShowShopItemUI(bool IsShow);
	UFUNCTION(BlueprintCallable)
	void HideShopItemUI(bool IsHide);
	UFUNCTION(BlueprintCallable)
	void ShowShopPriceUI();
	UFUNCTION(BlueprintCallable)
	void HideShopPriceUI();
	UFUNCTION(BlueprintCallable)
	void SetItemComponentAttachInCurSelect();
	UFUNCTION(BlueprintCallable)
	void SetShopItemUI();
	UFUNCTION(BlueprintCallable)
	void SetShopItemUIAttributes();
	UFUNCTION(BlueprintCallable)
	void SetShopItemPriceUIAttributes();

	void HideMe();
	void ShowMe();
};



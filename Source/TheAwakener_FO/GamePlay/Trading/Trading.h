#pragma once
#include "CoreMinimal.h"
#include "Deal.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "Trading.generated.h"

/**
 * 一项交易内容
 */
USTRUCT(BlueprintType)
struct FTrading
{
	GENERATED_BODY()
public:
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FDeal> Deals;

	//交易失败的时候播放的默认音效
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString FailSFX = "";

	TArray<FJsonFuncData> OnDeal;  //(FTrading Trading, FDeal Deal)=>FTrading
	TArray<FJsonFuncData> OnTimeElapsed;	//(FTrading)=>FTrading

	//货币图标的路径，每次交易如果需要显示某些货币就要配置这个，并且不同交易中同一种货币可以是不同图标
	TMap<FString, FString> CurrencyIconPath;

	FTrading():Id(""),Deals(TArray<FDeal>()),OnDeal(TArray<FJsonFuncData>()),OnTimeElapsed(TArray<FJsonFuncData>()){};
	FTrading(
		FString TradingId,
		TArray<FDeal> TradingDeals,
		TArray<FJsonFuncData> TradingOnDeal,
		TArray<FJsonFuncData> TradingOnTimeElapsed)
	{
		Id = TradingId;
		Deals = TradingDeals;
		OnDeal = TradingOnDeal;
		OnTimeElapsed = TradingOnTimeElapsed;
	};

	static FTrading FromJson(TSharedPtr<FJsonObject> JsonObj);
	
	static FTrading DoOnDeal(FJsonFuncData JsonFuncData, FTrading Trading, FDeal Deal);

	static FTrading DoOnTimeElapsed(FJsonFuncData JsonFuncData, FTrading Trading);

	/**
	 * 改变当前所有物品的售价
	 * @param Percentage 百分比变化，1代表原价
	 */
	void ModifyAllPrice(float Percentage);
};

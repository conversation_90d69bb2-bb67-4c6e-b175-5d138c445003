#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/HitBox/AttackHitBox.h"
#include "Flagmark.h"

#include "Attacks.generated.h"
class AAwCharacter;
class AAwBullet;
/**
 * The ability to do a melee attack.
 */
// USTRUCT(BlueprintType)
// struct THEAWAKENER_FO_API FAttacks
// {
// 	GENERATED_BODY()
 
//   public:

// 	/**
// 	 * The animation curve for the attack.
// 	 */
//
// 	AOE,UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	FRuntimeFloatCurve Animation;

// 	/**
// 	 * The attack range.
// 	 */
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	float Range = 50.0f;

// 	/**
// 	 * The damage dealt by an attack.
// 	 */
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	int32 Damage = 10;

// 	/**
// 	 * The exact attack frame time.
// 	 */
// 	UPROPERTY(BlueprintReadWrite, EditAnywhere)
// 	float FrameTime = 0.5f;
// };
UENUM()
enum EAttackBoxType
{
	<PERSON><PERSON>,
	<PERSON>et,
	<PERSON><PERSON>,
};
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FTrAttackBox
{
	GENERATED_BODY()
	public:
	bool bIsECSPositionSynced = false;
	float Radius = 25;
	UPROPERTY()
	UAttackHitBox* HitBox = nullptr;
	UPROPERTY()
	AAwCharacter* Attacker = nullptr;
	EAttackBoxType BoxType;
	UPROPERTY()
	AAwBullet* Bullet = nullptr;
	UPROPERTY()
	int32 BulletModelKey = -1;
	//由管理器抽选，每次攻击都会不同，相同技能的同一次攻击的多个碰撞盒用一个Flag
	EFlagmarkBit AttackFlag;
	FORCEINLINE
	FTrAttackBox(const float InRadius,AAwCharacter* InCharacter,AAwBullet* InBullet,UAttackHitBox* InHitBox):
	Radius(InRadius),HitBox(InHitBox),Attacker(InCharacter),Bullet(InBullet){}
	FTrAttackBox(const float InRadius,UAttackHitBox* InHitBox):
	Radius(InRadius),HitBox(InHitBox){}
	FORCEINLINE
	FTrAttackBox(){}
};

USTRUCT(BlueprintType)
struct FTrAttackEnable
{
	GENERATED_BODY()
	
};

// Fill out your copyright notice in the Description page of Project Settings.


#include "GlowExtendTweenInfo.h"

bool FGlowExtendTweenInfo::BetterThan(const FGlowExtendTweenInfo& Other) const{
	if (Other.Falling() == true)
	{
		if (this->Falling() == false ) return true;
	}else
	{
		if (this->Falling() == true) return false;
	}
	return this->RemainTime() > Other.RemainTime();
}


float FGlowExtendTweenInfo::RemainTime() const
{
	return this->TotalTime - this->TimeElapsed;
}

bool FGlowExtendTweenInfo::Falling() const
{
	const float ToEnd = this->TotalTime - this->TimeElapsed;
	const float ToEndBright = Fall > 0 ? (ToEnd * Fall) : 1;
	return ToEndBright < 1;
}

float FGlowExtendTweenInfo::ThisTickValue() const
{
	if (this->TimeElapsed >= this->TotalTime || this->TimeElapsed <= 0)
		return 0;
	
	const float TotalShine = this->TimeElapsed * ShiningModifer;
	float CurShine = 1;
	if (this->ShiningModifer > 0 && this->ShiningDarkness > 0)
	{
		CurShine = TotalShine;
		const float Wave2 = (2 * FMath::Min(ShiningDarkness, 1.00f));
		while (CurShine > Wave2) CurShine -= Wave2;
		if (CurShine < Wave2 / 2) CurShine = 1 - CurShine; else CurShine = 1 - ShiningDarkness + CurShine;
	}

	//结尾阶段
	const float ToEnd = this->TotalTime - this->TimeElapsed;
	const float ToEndBright = Fall > 0 ? (ToEnd * Fall) : 1;
	if (ToEndBright < 1)
	{
		return FMath::Min(CurShine, ToEndBright);
	}

	//开头阶段
	const float ToMiddleBright = Growth > 0 ? FMath::Min(1.00f, this->TimeElapsed * Growth) : 1;
	if (ToMiddleBright < 1)
	{
		return FMath::Min(ToMiddleBright, CurShine);
	}

	//中间阶段
	return CurShine;
}

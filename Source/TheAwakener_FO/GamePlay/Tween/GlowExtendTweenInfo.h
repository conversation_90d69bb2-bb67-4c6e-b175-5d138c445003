// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "GlowExtendTweenInfo.generated.h"

/**
 * 外发光变化的Tween的信息
 */
USTRUCT(BlueprintType)
struct FGlowExtendTweenInfo
{
	GENERATED_BODY()
public:
	
	//从Start到End，每秒增加多少
	UPROPERTY()
	float Growth = 0;
	//从End到Start，每秒衰减多少
	UPROPERTY()
	float Fall = 0;
	//闪烁的幅度，End-这个值=闪烁最暗的时刻
	UPROPERTY()
	float ShiningDarkness = 0;
	//闪烁频率，每秒闪烁度变化值（绝对值）
	UPROPERTY()
	float ShiningModifer = 0;

	//当前闪烁方向是否为减少亮度
	UPROPERTY()
	bool IsNegative = true;
	//总多少秒
	UPROPERTY()
	float TotalTime = 0;
	//经过了多少时间
	UPROPERTY()
	float TimeElapsed = 0;
	
	UPROPERTY()
	FLinearColor Color = FLinearColor(23,7,0);
	
	FGlowExtendTweenInfo(){};
	FGlowExtendTweenInfo(float BlendIn, float BlendOut):
		 Growth(BlendIn > 0 ? 1/BlendIn : 0), Fall(BlendOut > 0 ? 1/BlendOut : 0){};

	//这一帧Extends是多少
	float ThisTickValue() const;

	//是否已经在结束的时刻了（就是在BlendOut了）
	bool Falling() const;

	//还剩下多久(秒)
	float RemainTime() const;

	//是否比另一个更加合适作为当前的
	bool BetterThan(const FGlowExtendTweenInfo& Other) const;
};

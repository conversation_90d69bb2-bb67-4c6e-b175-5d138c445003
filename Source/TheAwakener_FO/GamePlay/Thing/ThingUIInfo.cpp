// Fill out your copyright notice in the Description page of Project Settings.


#include "ThingUIInfo.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

FThingUIInfo FThingUIInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
    FThingUIInfo Res = FThingUIInfo();
    Res.Description = JsonObj->GetStringField("Description");
    Res.Icon = JsonObj->GetStringField("Icon");
    Res.Name = JsonObj->GetStringField("Name");
    Res.Rank = JsonObj->GetIntegerField("Rank");
    Res.ThingId = JsonObj->GetStringField("ThingId");
    Res.ThingType = UDataFuncLib::FStringToEnum<EThingType>(JsonObj->GetStringField("ThingType"));
	Res.SceneCameraLocation.InitFromString(JsonObj->GetStringField("SceneCameraLocation"));
	Res.ModelRotator.InitFromString(JsonObj->GetStringField("ModelRotator"));
	Res.ModelLocation.InitFromString(JsonObj->GetStringField("ModelLocation"));
	return Res;
}

/**
 * 作为一个道具的Icon
 * @param IndexInRole 这个道具位于背包第几个，如果是-1，AsItem才会生效
 * @param AsItem 这个道具的Id，如果Index<0，就会需要这个，毕竟“画饼”的时候可没有实际东西
 * @param CreatorSign 道具制作者签名，这个玩法在后期会有用
 */
FThingUIInfo FThingUIInfo::AsItem(int IndexInRole, FString AsItem, FString CreatorSign)
{
     const FString CheckThingId = IndexInRole >= 0 && IndexInRole < UAwGameInstance::Instance->RoleInfo.ItemObjs.Num() ?
           UAwGameInstance::Instance->RoleInfo.ItemObjs[IndexInRole].Model.Id : AsItem;
     if (CheckThingId.IsEmpty()) return FThingUIInfo();
     FThingUIInfo Res = UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(EThingType::Item, CheckThingId);
     Res.CreatorName = CreatorSign;
     return Res;
}

/**
 * 作为一个防具的Icon
 * @param IndexInRole 这个防具位于背包第几个，如果是-1，EquipmentId才会生效
 * @param EquipmentId 防具的Id，在背包里都找不着的话，就会走这个，毕竟穿在身上的背包里拿不到
 * @param CreatorSign 防具打造者签名，这个玩法在后期会有用
 */
FThingUIInfo FThingUIInfo::AsEquipment(int IndexInRole, FString EquipmentId, FString CreatorSign)
{
      const FString CheckThingId = IndexInRole >= 0 && IndexInRole < UAwGameInstance::Instance->RoleInfo.EquipmentObjs.Num() ?
            UAwGameInstance::Instance->RoleInfo.EquipmentObjs[IndexInRole].Id : EquipmentId;
      if (CheckThingId.IsEmpty()) return FThingUIInfo();
      FThingUIInfo Res = UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(EThingType::Equipment, CheckThingId);
      Res.CreatorName = CreatorSign;
      return Res;
}

/**
 * 作为一个武器部件（WeaponModel）的Icon
 * @param IndexInRole 这个武器部件位于背包第几个，如果是-1，AsItem才会生效
 * @param WeaponModelId 武器部件的Id，在背包里都找不着的话，就会走这个
 */
FThingUIInfo FThingUIInfo::AsWeaponModel(int IndexInRole, FString WeaponModelId)
{
	      const FString CheckThingId = IndexInRole >= 0 && IndexInRole < UAwGameInstance::Instance->RoleInfo.WeaponObjs.Num() ?
	            UAwGameInstance::Instance->RoleInfo.WeaponObjs[IndexInRole].Model.Id : WeaponModelId;
	      if (CheckThingId.IsEmpty()) return FThingUIInfo();
	      return UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(EThingType::WeaponModel, CheckThingId);
}

/**
 * 作为一个武器（WeaponObj）的Icon。TODO：目前只根据武器类型获取图标，之后肯定是要改的
 * @param IndexInRole 这个武器位于背包第几个，如果是-1，WeaponType才会生效
 * @param WeaponType 武器的类型，在背包里都找不着的话，就会走这个，毕竟装备的武器是背包里拿不到的
 * @param CreatorSign 武器打造者签名，这个玩法在后期会有用
 */
FThingUIInfo FThingUIInfo::AsWeaponObj(int IndexInRole, EClassWeaponType WeaponType, FString CreatorSign)
{
      constexpr EClassWeaponType CheckWeaponType = EClassWeaponType::BigSword;
				//IndexInRole >= 0 && IndexInRole < UAwGameInstance::Instance->RoleInfo.WeaponObjs.Num() ?
				//  UAwGameInstance::Instance->RoleInfo.WeaponObjs[IndexInRole].Model.WeaponType : WeaponType;
	FString CheckThingId =UDataFuncLib::EnumToFString(CheckWeaponType);
	
	if (CheckThingId.IsEmpty()) return FThingUIInfo();
	
	FThingUIInfo Res = UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(EThingType::WeaponObj, CheckThingId);
	Res.CreatorName = CreatorSign;
	return Res;
}

/**
 * 作为货币的Icon
 * @param CurrencyId 哪一种货币
 */
FThingUIInfo FThingUIInfo::AsCurrency(FString CurrencyId )
{
	return UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(EThingType::Currency, CurrencyId);
}
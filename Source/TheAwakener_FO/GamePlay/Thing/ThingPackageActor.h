// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/WidgetComponent.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Role/AwRoleInfo.h"
#include "ThingPackageActor.generated.h"

UCLASS()
class THEAWAKENER_FO_API AThingPackageActor : public AActor
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AThingPackageActor();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	float DropTime = 0.3;
	float DropHeight = 200;
	bool bIsDropping = false;
	float Timing = 0;
	FVector OriLocation = FVector::ZeroVector;
	
	float LifeTime = 30.0f;
	bool ToBeRemoved = false;
	
	UPROPERTY(BlueprintReadWrite)
	float PickUpDis = 300;
	UPROPERTY(BlueprintReadWrite)
	float MinPickUpDis = 50;
	UPROPERTY(BlueprintReadWrite)
	AAwCharacter* ConditionCharacter;
	UPROPERTY(BlueprintReadWrite)
	AAwCharacter* TargetCharacter;

	TArray<FThingObj> ThingObjs = TArray<FThingObj>();
 
	UFUNCTION(BlueprintCallable)
	void PlayDropAnim();
	void Dropping(float DeltaTime);

	UPROPERTY()
	UWidgetComponent* InteractUI;
	UFUNCTION(BlueprintCallable)
	void ShowUI();
	UFUNCTION(BlueprintCallable)
	void HideUI();

	void Preorder(AAwCharacter* Character);
	void CancelPreorder(AAwCharacter* Character);
	void SignUp(AAwPlayerController* PlayerController);
	void PickUp(AAwCharacter* Character);
	
	UPROPERTY()
	TArray<AAwCharacter*> CanPickUpChas = TArray<AAwCharacter*>();
	UPROPERTY()
	TArray<AAwPlayerController*> SignedPlayerControllers = TArray<AAwPlayerController*>();
	/**
	 * @brief 哪个角色预约了要拾取我（已经按下拾取，在做拾取动作，但是还没拾取完成）
	 */
	UPROPERTY()
	TArray<AAwCharacter*> PreorderCharacter = TArray<AAwCharacter*>();

	void RemoveMySelf();
	UFUNCTION(BlueprintNativeEvent)
	void RemoveEvent();

	UFUNCTION()
	bool CanBeLoot(AAwCharacter* Picker) const;
};

// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "SurvivorEventFunc.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API USurvivorEventFunc : public UObject
{
	GENERATED_BODY()
	static constexpr int SpawnPointOffsetHeight = 600;
	public:
	/// 
	/// @param eventIndex GameMode自动处理
	/// @param Params 间隔秒数
	/// @return 
	UFUNCTION(BlueprintCallable, Category = "SurvivorEventCondition")
	bool EveryNSeconds(int eventIndex, TArray<FString> Params);

	UFUNCTION(BlueprintCallable, Category = "SurvivorEventCondition")
	bool MobLessThan(int eventIndex, TArray<FString> Params);
	
	//检测塔是否到达指定点
	UFUNCTION(BlueprintCallable, Category = "SurvivorEventCondition")
	bool CheckTowerReachPoint(int eventIndex, TArray<FString> Params);
	//检测塔是否到达指定点
	UFUNCTION(BlueprintCallable, Category = "SurvivorEventCondition")
	bool CheckTowerNotAtPoint(int eventIndex, TArray<FString> Params);
	//检测幸存者开关
	UFUNCTION(BlueprintCallable, Category = "SurvivorEventCondition")
	bool CheckSurvivorSwitch(int eventIndex, TArray<FString> Params);
	
	//float radius,int points
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionBeforeTower(TArray<FString> Params);
	
	//float radius,int points
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionAroundPlayerCircle(TArray<FString> Params);
	
	//float length,int points
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionAroundPlayerSquare(TArray<FString> Params);
	//float radius,int points
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionAroundTowerCircle(TArray<FString> Params);
	
	//float length,int points
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionAroundTowerSquare(TArray<FString> Params);
	
	//float radius,int points
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	static TArray<FTransform> PositionAroundTargetCircle(AAwCharacter* Target,float radius ,int points);
	
	//float length,int points
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	static TArray<FTransform> PositionAroundTargetSquare(AAwCharacter* Target,float length ,int points);
	//TArray<int> indexes
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionFromNormalPoints(TArray<FString> Params);
	
	//TArray<int> indexes
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionFromElitePoints(TArray<FString> Params);
	
	//TArray<int> indexes
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	TArray<FTransform> PositionFromBossPoints(TArray<FString> Params);
	
	//TArray<int> indexes
	UFUNCTION()
	static TArray<FTransform> PositionFromExistPoints(TArray<AActor*> SpawnPoints, TArray<int> indexes);
	
	//TArray<int> indexes
	UFUNCTION(BlueprintCallable, Category = "SurvivorSpawnPosition")
	static TArray<FTransform> SpreadToPoints(TArray<FTransform> Points,int count);
};



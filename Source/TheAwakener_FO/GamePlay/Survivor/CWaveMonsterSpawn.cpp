// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "CWaveMonsterSpawn.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"

FCWaveEvent FCWaveEvent::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FCWaveEvent Result;
	if (JsonObj.IsValid())
	{
		Result.Id = UDataFuncLib::AwGetStringField(JsonObj,"Id","");
		Result.TimeLimit = UDataFuncLib::AwGetNumberField(JsonObj,"TimeLimit",-1.0f);
		Result.Conditions = UDataFuncLib::AwGetStringArrayField(JsonObj, "Conditions");
		Result.MonsterSetId = UDataFuncLib::AwGetStringField(JsonObj,"MonsterSetId","");
		Result.RequireEventComplete = UDataFuncLib::AwGetStringField(JsonObj,"RequireEventComplete","");
		JsonObj->GetObjectField("TargetNum")
	}
	return Result;
}

FCWaveMonsterSpawn FCWaveMonsterSpawn::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FCWaveMonsterSpawn Result;

	if (JsonObj.IsValid())
	{
		Result.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id", "");
		Result.TimeStart = UDataFuncLib::AwGetNumberField(JsonObj, "TimeStart", 0.0f);
		Result.TimeEnd = UDataFuncLib::AwGetNumberField(JsonObj, "TimeEnd", 0.0f);
		Result.Conditions = UDataFuncLib::AwGetStringArrayField(JsonObj, "Conditions");
		Result.Availability = UDataFuncLib::AwGetNumberField(JsonObj, "Availability", 1);

		// 解析MonsterSets数组
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "MonsterSets"))
		{
			if (Value.IsValid() && Value->Type == EJson::Object)
			{
				FCMonsterSpawnSet MonsterSet = FCMonsterSpawnSet::FromJson(Value->AsObject());
				Result.MonsterSets.Add(MonsterSet);
			}
		}

		Result.PositionType = UDataFuncLib::AwGetEnumField(JsonObj, "PositionType", EWaveEventPositionType::NormalPoints);

		// 解析PositionsParams数组
		for (const TSharedPtr<FJsonValue> Value : UDataFuncLib::AwGetArrayField(JsonObj, "PositionsParams"))
		{
			if (Value.IsValid() && Value->Type == EJson::Number)
			{
				Result.PositionsParams.Add(Value->AsNumber());
			}
		}

		Result.RequireEventComplete = UDataFuncLib::AwGetStringField(JsonObj, "RequireEventComplete", "");
	}

	return Result;
}

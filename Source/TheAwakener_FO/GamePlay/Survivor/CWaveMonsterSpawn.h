// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "CMonsterSpawnSet.h"
#include "CWaveMonsterSpawn.generated.h"
UENUM(BlueprintType)
enum class EWaveEventPositionType : uint8
{
	NormalPoints,
	ElitePoints,
	BossPoints,
	SpecialPoints,
	AroundPlayerCircle,//PositionAroundPlayerCircle(300,10)  半径，点数
	AroundPlayerSquare,
	AroundTowerCircle,
	AroundTowerSquare,
	BeforeTower
};
/**
 * 关联FSurvivorEventState使用
 */
USTRUCT(BlueprintType)
struct  FCWaveEvent
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip="可以当备注用,地图用EventId组合"))
	FString Id;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TimeLimit;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Conditions;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MonsterSetId;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<int,int32> TargetNum;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip="前置EventId"))
	FString RequireEventComplete;
	static FCWaveEvent FromJson(TSharedPtr<FJsonObject> JsonObj);
};

USTRUCT(BlueprintType)
struct  FCWaveMonsterSpawn
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip="可以当备注用,地图用EventId组合"))
	FString Id;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TimeStart;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float TimeEnd;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> Conditions;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Availability;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FCMonsterSpawnSet> MonsterSets;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EWaveEventPositionType PositionType;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip="根据PositionType，Points类时参数为点的序号，Around类时参数为半径和点数"))
	TArray<int32> PositionsParams;
	UPROPERTY(BlueprintReadWrite, EditAnywhere,meta=(ToolTip="前置EventId"))
	FString RequireEventComplete;
	static FCWaveMonsterSpawn FromJson(TSharedPtr<FJsonObject> JsonObj);
};

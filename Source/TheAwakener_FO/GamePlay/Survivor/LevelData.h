#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "LevelData.generated.h"

UENUM(BlueprintType)
enum class ELevelType : uint8
{
	RPG,
	Rogue,
	Survivor,
};

// 关卡选择数据结构 - 对应JSON格式
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FLevelSelectionData : public FTableRowBase
{
	GENERATED_BODY()

public:
	// 关卡ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Selection")
	FString ID;

	// 分组ID
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Selection")
	FString GroupdID;

	// 关卡类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Selection")
	ELevelType Type;

	// 有效标记（正在制作，不能玩）
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Selection")
	bool Valid = false;
	
	// 关卡文件地址
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Selection")
	FString LevelPath;

	// 默认构造函数
	FLevelSelectionData()
		: ID("")
		, GroupdID("")
		, Type(ELevelType::Survivor)
	{
	}

	// 带参数构造函数
	FLevelSelectionData(const FString& InID, const FString& InGroupdID, ELevelType InType, bool InValid = false)
		: ID(InID)
		, GroupdID(InGroupdID)
		, Type(InType)
		, Valid(InValid)
	{
	}

	// 相等比较操作符
	bool operator==(const FLevelSelectionData& Other) const
	{
		return ID == Other.ID && GroupdID == Other.GroupdID && Type == Other.Type;
	}

	// 从JSON对象创建
	static FLevelSelectionData FromJson(TSharedPtr<FJsonObject> JsonObj);
};

USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FLevelIntroduce
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Introduce")
	FString ID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Introduce")
	FString Image;

	// 介绍信息的键值对映射
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Introduce")
	TMap<FString, FString> Introduce;

	// 从JSON对象创建
	static FLevelIntroduce FromJson(TSharedPtr<FJsonObject> JsonObj);

};
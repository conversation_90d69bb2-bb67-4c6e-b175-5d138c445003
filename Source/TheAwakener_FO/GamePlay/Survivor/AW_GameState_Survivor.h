// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameState.h"
#include "TheAwakener_FO/GamePlay/Survivor/SurvivorEventState.h"
#include "AW_GameState_Survivor.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API AAW_GameState_Survivor : public AAwGameState
{
	GENERATED_BODY()
public:
	//需要在蓝图内初始化事件状态
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TMap<int,FSurvivorEventState> EventStates;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FSurvivorSpawnData SpawnData;
	UFUNCTION(BlueprintCallable)
	void SetupPointsData(TArray<AActor*> Normal,TArray<AActor*> Elite,TArray<AActor*> Boss,
		TArray<AActor*> TowerCheckPoint,TArray<AActor*> Special);
	//守塔的目标（雅典娜）
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	AAwCharacter* AthenaTower;
	//塔前方定位
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	AAwCharacter* AthenaTowerLeader;
	//塔前方定位
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	AActor* AthenaTowerLeaderSpawnPoint;

	UFUNCTION(BlueprintPure, Category = "EventStates")
	FSurvivorEventState GetEventState(int Id)
	{
		FSurvivorEventState Result = FSurvivorEventState();
		Result.Index = Id;
		if (EventStates.Contains(Id))
			Result = EventStates[Id];
		return Result;
	}
	
	UFUNCTION(BlueprintCallable, Category = "EventStates")
	void UpdateEventState(int Id,FSurvivorEventState newState)
	{
		if (EventStates.Contains(Id))
			EventStates[Id] = newState;
		else
			EventStates.Add(Id, newState);
	}
};

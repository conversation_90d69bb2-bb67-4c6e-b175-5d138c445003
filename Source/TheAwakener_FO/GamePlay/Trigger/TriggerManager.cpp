// Fill out your copyright notice in the Description page of Project Settings.


#include "TriggerManager.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameInstance.h"

UTriggerManager::UTriggerManager()
{

}

void UTriggerManager::Init(UAwDataManager* DataManager, FString MapName)
{
	OverlapBeginTriggerList.Empty();
	OverlapEndTriggerList.Empty();
	CharacterDeadTriggerList.Empty();
	SceneItemDestroyTriggerList.Empty();
	
	TArray<FAwTrigger> AllTriggerInMap = DataManager->GetAllTriggerModel();
	for (int i = 0; i < AllTriggerInMap.Num(); i++)
	{
		switch (AllTriggerInMap[i].TriggerType)
		{
		case ETriggerPointType::OverlapBegin:
			OverlapBeginTriggerList.Add(AllTriggerInMap[i]);
			break;
		case ETriggerPointType::OverlapEnd:
			OverlapEndTriggerList.Add(AllTriggerInMap[i]);
			break;
		case ETriggerPointType::CharacterDead:
			CharacterDeadTriggerList.Add(AllTriggerInMap[i]);
			break;
		case ETriggerPointType::SceneItemDestroyed:
			SceneItemDestroyTriggerList.Add(AllTriggerInMap[i]);
			break;
		default:
			{
				bool bInitTrigger = false;
				for (int j = 0; j < AlwaysTriggerList.Num(); j++)
				{
					if (AlwaysTriggerList[j].Id == AllTriggerInMap[i].Id)
					{
						bInitTrigger = true;
						break;
					}
				}
				if(!bInitTrigger)
					AlwaysTriggerList.Add(AllTriggerInMap[i]);
				break;
			}
		
		}
	}
}

void UTriggerManager::Tick(float DeltaTime)
{
	//Always Trigger
	TriggerAllTick();
	//OverlapBegin
	if(ThisTickHasOverlapBegin)
	{
		TriggerAllOverlapBeginList();
		ThisTickHasOverlapBegin = false;
	}
	//OverlapEnd
	if(ThisTickHasOverlapEnd)
	{
		TriggerAllOverlapEndList();
		ThisTickHasOverlapEnd = false;
	}
	//CharacterDead
	if(ThisTickHasCharacterDead)
	{
		TriggerAllCharacterDeadList();
		ThisTickHasCharacterDead = false;
	}
	//SceneItemDestroyed
	if(ThisTickHasSceneItemDestroy)
	{
		TriggerAllSceneItemDestroyList();
		ThisTickHasSceneItemDestroy = false;
	}
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		GameState->ThisTickDeathCharacters.Empty();
		GameState->ThisTickDestroySceneItemList.Empty();
	}
}

void UTriggerManager::TriggerAllTick()
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	TArray<FAwTrigger> RemoveTriggers;
	for (int i = 0; i < AlwaysTriggerList.Num(); i++)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : AlwaysTriggerList[i].Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}ConditionFuncParam;
				ConditionFuncParam.Params = Condition.Params;
				this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
				if(!ConditionFuncParam.Result)
				{
					CanTrigge = false;
					break;
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : AlwaysTriggerList[i].Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					struct
					{
						TArray<FString> Params;
						UTimelineNode* Result = nullptr;
					}ActionFuncParam;
					ActionFuncParam.Params = Action.Params;
					this->ProcessEvent(ActionFunc, &ActionFuncParam);
					if (ActionFuncParam.Result)
					{
						GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
					}
				}
			}
			if(AlwaysTriggerList[i].bUseOnce)
				RemoveTriggers.Add(AlwaysTriggerList[i]);
		}
	}
	for(auto Trigger : RemoveTriggers)
	{
		RemoveTrigger(Trigger);
	}
}

void UTriggerManager::TriggerCurMapOnCreate(FString LevelName, FTransform LevelTrans)
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	//LevelOnCreate
	for (auto Script : UGameplayFuncLib::GetAwDataManager()->GetMapInfoByLevelPath(LevelName).OnCreate)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : Script.Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				if(ConditionFunc->NumParms == 4)
				{
					struct
					{
						FString LevelName;
						FTransform LevelTrans;
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.LevelName = LevelName;
					ConditionFuncParam.LevelTrans = LevelTrans;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
				else
				{
					struct
					{
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : Script.Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					if(ActionFunc->NumParms == 4)
					{
						struct
						{
							FString LevelName;
							FTransform LevelTrans;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.LevelName = LevelName;
						ActionFuncParam.LevelTrans = LevelTrans;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
					else
					{
						struct
						{
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
				}
			}
		}
	}
}

void UTriggerManager::TriggerAllMapOnCreate(FString LevelName, FTransform LevelTrans)
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	TArray<FAwTrigger> RemoveTriggers;
	for (auto Script : MapOnCreateTriggerList)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : Script.Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				if(ConditionFunc->NumParms == 4)
				{
					struct
					{
						FString LevelName;
						FTransform LevelTrans;
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.LevelName = LevelName;
					ConditionFuncParam.LevelTrans = LevelTrans;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
				else
				{
					struct
					{
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : Script.Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					if(ActionFunc->NumParms == 4)
					{
						struct
						{
							FString LevelName;
							FTransform LevelTrans;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.LevelName = LevelName;
						ActionFuncParam.LevelTrans = LevelTrans;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
					else
					{
						struct
						{
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
				}
			}
			if(Script.bUseOnce)
				RemoveTriggers.Add(Script);
		}
	}
	for(auto Trigger : RemoveTriggers)
	{
		RemoveTrigger(Trigger);
	}
}

void UTriggerManager::TriggerCurMapOnRemoved(FString LevelName)
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	//LevelOnRemoved
	for (auto Script : UGameplayFuncLib::GetAwDataManager()->GetMapInfoByLevelPath(LevelName).OnRemoved)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : Script.Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				if(ConditionFunc->NumParms == 3)
				{
					struct
					{
						FString LevelName;
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.LevelName = LevelName;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
				else
				{
					struct
					{
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : Script.Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					if(ActionFunc->NumParms == 3)
					{
						struct
						{
							FString LevelName;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.LevelName = LevelName;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
					else
					{
						struct
						{
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
				}
			}
		}
	}
}

void UTriggerManager::TriggerAllMapOnRemoved(FString LevelName)
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	TArray<FAwTrigger> RemoveTriggers;
	for (auto Script : MapOnRemovedTriggerList)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : Script.Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				if(ConditionFunc->NumParms == 3)
				{
					struct
					{
						FString LevelName;
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.LevelName = LevelName;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
				else
				{
					struct
					{
						TArray<FString> Params;
						bool Result;
					}ConditionFuncParam;
					ConditionFuncParam.Params = Condition.Params;
					this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
					if(!ConditionFuncParam.Result)
					{
						CanTrigge = false;
						break;
					}
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : Script.Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					if(ActionFunc->NumParms == 3)
					{
						struct
						{
							FString LevelName;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.LevelName = LevelName;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
					else
					{
						struct
						{
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}ActionFuncParam;
						ActionFuncParam.Params = Action.Params;
						this->ProcessEvent(ActionFunc, &ActionFuncParam);
						if (ActionFuncParam.Result)
						{
							GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
						}
					}
				}
			}
			if(Script.bUseOnce)
				RemoveTriggers.Add(Script);
		}
	}
	for(auto Trigger : RemoveTriggers)
	{
		RemoveTrigger(Trigger);
	}
}

void UTriggerManager::TriggerAllOverlapBeginList()
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	TArray<FAwTrigger> RemoveTriggers;
	for (int i = 0; i < OverlapBeginTriggerList.Num(); i++)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : OverlapBeginTriggerList[i].Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}ConditionFuncParam;
				ConditionFuncParam.Params = Condition.Params;
				this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
				if(!ConditionFuncParam.Result)
				{
					CanTrigge = false;
					break;
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : OverlapBeginTriggerList[i].Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					struct
					{
						TArray<FString> Params;
						UTimelineNode* Result = nullptr;
					}ActionFuncParam;
					ActionFuncParam.Params = Action.Params;
					this->ProcessEvent(ActionFunc, &ActionFuncParam);
					if (ActionFuncParam.Result)
					{
						GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
					}
				}
			}
			if(OverlapBeginTriggerList[i].bUseOnce)
				RemoveTriggers.Add(OverlapBeginTriggerList[i]);
		}
	}
	for(auto Trigger : RemoveTriggers)
	{
		RemoveTrigger(Trigger);
	}
}

void UTriggerManager::TriggerAllOverlapEndList()
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	TArray<FAwTrigger> RemoveTriggers;
	for (int i = 0; i < OverlapEndTriggerList.Num(); i++)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : OverlapEndTriggerList[i].Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}ConditionFuncParam;
				ConditionFuncParam.Params = Condition.Params;
				this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
				if(!ConditionFuncParam.Result)
				{
					CanTrigge = false;
					break;
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : OverlapEndTriggerList[i].Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					struct
					{
						TArray<FString> Params;
						UTimelineNode* Result = nullptr;
					}ActionFuncParam;
					ActionFuncParam.Params = Action.Params;
					this->ProcessEvent(ActionFunc, &ActionFuncParam);
					if (ActionFuncParam.Result)
					{
						GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
					}
				}
			}
			if(OverlapEndTriggerList[i].bUseOnce)
				RemoveTriggers.Add(OverlapEndTriggerList[i]);
		}
	}
	for(auto Trigger : RemoveTriggers)
	{
		RemoveTrigger(Trigger);
	}
}
//Todo:待完善调用端
void UTriggerManager::TriggerAllCharacterDeadList()
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	TArray<FAwTrigger> RemoveTriggers;
	for (int i = 0; i < CharacterDeadTriggerList.Num(); i++)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : CharacterDeadTriggerList[i].Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}ConditionFuncParam;
				ConditionFuncParam.Params = Condition.Params;
				this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
				if(!ConditionFuncParam.Result)
				{
					CanTrigge = false;
					break;
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : CharacterDeadTriggerList[i].Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					struct
					{
						TArray<FString> Params;
						UTimelineNode* Result = nullptr;
					}ActionFuncParam;
					ActionFuncParam.Params = Action.Params;
					this->ProcessEvent(ActionFunc, &ActionFuncParam);
					if (ActionFuncParam.Result)
					{
						GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
					}
				}
			}
			if(CharacterDeadTriggerList[i].bUseOnce)
				RemoveTriggers.Add(CharacterDeadTriggerList[i]);
		}
	}
	for(auto Trigger : RemoveTriggers)
	{
		RemoveTrigger(Trigger);
	}
}

void UTriggerManager::TriggerAllSceneItemDestroyList()
{
	UAwGameInstance* GameInstance = Cast<UAwGameInstance>(GWorld->GetGameInstance());
	TArray<FAwTrigger> RemoveTriggers;
	for (int i = 0; i < SceneItemDestroyTriggerList.Num(); i++)
	{
		//Condition
		bool CanTrigge = true;
		for(auto Condition : SceneItemDestroyTriggerList[i].Condition)
		{
			UFunction* ConditionFunc;
			ConditionFunc = UCallFuncLib::GetUFunction(Condition.ClassPath, Condition.FunctionName);
			if (ConditionFunc)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}ConditionFuncParam;
				ConditionFuncParam.Params = Condition.Params;
				this->ProcessEvent(ConditionFunc, &ConditionFuncParam);
				if(!ConditionFuncParam.Result)
				{
					CanTrigge = false;
					break;
				}
			}
		}
		if (CanTrigge)
		{
			//Action
			for(auto Action : SceneItemDestroyTriggerList[i].Action)
			{
				UFunction* ActionFunc;
				ActionFunc = UCallFuncLib::GetUFunction(Action.ClassPath, Action.FunctionName);
				if (ActionFunc)
				{
					struct
					{
						TArray<FString> Params;
						UTimelineNode* Result = nullptr;
					}ActionFuncParam;
					ActionFuncParam.Params = Action.Params;
					this->ProcessEvent(ActionFunc, &ActionFuncParam);
					if (ActionFuncParam.Result)
					{
						GameInstance->TimelineManager->AddNode(ActionFuncParam.Result);
					}
				}
			}
			if(SceneItemDestroyTriggerList[i].bUseOnce)
				RemoveTriggers.Add(SceneItemDestroyTriggerList[i]);
		}
	}
	for(auto Trigger : RemoveTriggers)
	{
		RemoveTrigger(Trigger);
	}
}

void UTriggerManager::AddTrigger(FAwTrigger Trigger)
{
	switch (Trigger.TriggerType)
	{
	case ETriggerPointType::Always:
		this->AlwaysTriggerList.Add(Trigger);
		break;
	case ETriggerPointType::MapOnCreate:
		this->MapOnCreateTriggerList.Add(Trigger);
		break;
	case ETriggerPointType::MapOnRemoved:
		this->MapOnRemovedTriggerList.Add(Trigger);
		break;
	case ETriggerPointType::OverlapBegin:
		this->OverlapBeginTriggerList.Add(Trigger);
		break;
	case ETriggerPointType::OverlapEnd:
		this->OverlapEndTriggerList.Add(Trigger);
		break;
	case ETriggerPointType::CharacterDead:
		this->CharacterDeadTriggerList.Add(Trigger);
		break;
	case ETriggerPointType::SceneItemDestroyed:
		this->SceneItemDestroyTriggerList.Add(Trigger);
		break;
	}
}

void UTriggerManager::RemoveTrigger(FAwTrigger Trigger)
{
	switch (Trigger.TriggerType)
	{
	case ETriggerPointType::Always:
		{
			if(AlwaysTriggerList.Contains(Trigger))
				AlwaysTriggerList.Remove(Trigger);
			break;
		}
	case ETriggerPointType::MapOnCreate:
		{
			if(MapOnCreateTriggerList.Contains(Trigger))
				MapOnCreateTriggerList.Remove(Trigger);
			break;
		}
	case ETriggerPointType::MapOnRemoved:
		{
			if(MapOnRemovedTriggerList.Contains(Trigger))
				MapOnRemovedTriggerList.Remove(Trigger);
			break;
		}
	case ETriggerPointType::OverlapBegin:
		{
			if(OverlapBeginTriggerList.Contains(Trigger))
				OverlapBeginTriggerList.Remove(Trigger);
			break;
		}
	case ETriggerPointType::OverlapEnd:
		{
			if(OverlapEndTriggerList.Contains(Trigger))
				OverlapEndTriggerList.Remove(Trigger);
			break;
		}
	case ETriggerPointType::CharacterDead:
		{
			if(CharacterDeadTriggerList.Contains(Trigger))
				CharacterDeadTriggerList.Remove(Trigger);
			break;
		}
	case ETriggerPointType::SceneItemDestroyed:
		{
			if(SceneItemDestroyTriggerList.Contains(Trigger))
				SceneItemDestroyTriggerList.Remove(Trigger);
			break;
		}
	}
}

void UTriggerManager::ClearAllNotOverAllTrigger()
{
	ClearNotOverAllTrigger(AlwaysTriggerList);
	ClearNotOverAllTrigger(OverlapBeginTriggerList);
	ClearNotOverAllTrigger(OverlapEndTriggerList);
	ClearNotOverAllTrigger(MapOnCreateTriggerList);
	ClearNotOverAllTrigger(MapOnRemovedTriggerList);
	ClearNotOverAllTrigger(CharacterDeadTriggerList);
	ClearNotOverAllTrigger(SceneItemDestroyTriggerList);
}

void UTriggerManager::AddOverlapBegin()
{
	ThisTickHasOverlapBegin = true;
}

void UTriggerManager::AddOverlapEnd()
{
	ThisTickHasOverlapEnd = true;
}

void UTriggerManager::AddCharacterDead(AAwCharacter* Character)
{
	ThisTickHasCharacterDead = true;
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		FString CharacterTag = "";
		if(GameState->AllCharacters.Contains(Character))
		{
			CharacterTag = GameState->AllCharacters[Character];
			//GameState->AllCharacters.Remove(Character);
		}
		GameState->ThisTickDeathCharacters.Add(Character, CharacterTag);
	}
}

void UTriggerManager::AddSceneItemDestroy(AAwSceneItem* SceneItem)
{
	ThisTickHasSceneItemDestroy = true;
	AAwGameState* GameState = UGameplayFuncLib::GetAwGameState();
	if(GameState)
	{
		const FString ItemId = SceneItem->Id;
		GameState->SceneItemList.Remove(ItemId);
		GameState->ThisTickDestroySceneItemList.Add(ItemId, SceneItem);
	}
}

void UTriggerManager::ClearNotOverAllTrigger(TArray<FAwTrigger>& TriggerList)
{
	TArray<int> RemovList;
	for(int i = 0; i < TriggerList.Num(); i++)
	{
		if(!TriggerList[i].bOverAllMap)
			RemovList.Add(i);
	}
	for(int i = 0; i < RemovList.Num(); i++)
	{
		TriggerList.RemoveAt(RemovList[i] - i);
	}
}

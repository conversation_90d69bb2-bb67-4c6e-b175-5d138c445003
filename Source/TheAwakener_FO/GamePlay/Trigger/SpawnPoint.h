// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "AwTrigger.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/NpcInfo.h"
#include "TheAwakener_FO/GamePlay/Quest/AwQuest.h"
#include "SpawnPoint.generated.h"

USTRUCT(BlueprintType)
struct FNPCSpawnBuffInfo
{
	GENERATED_BODY()
public:
	//Buff ID
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;
	//层数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int AddStack = 0;
};

USTRUCT(BlueprintType)
struct FNPCSpawnModel
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FNpcInfo NpcInfo;
	//当生成NPC后存在GameState里TMap CharacterList的Tag值(一般来说填的是触发TriggerCatcher的Id)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Tag;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	ETriggerPointType TriggerSpawnType = ETriggerPointType::Always;
	//是否生成过一次后就不用了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool bSpawnOnce = true;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> Condition;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> AIClips;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FNPCSpawnBuffInfo> Buffs;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnCreate;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PathNodeQueueId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int PathNodeQueueIndex = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwQuestComponentData QuestData;

	static FNPCSpawnBuffInfo SplitBuffIDAndStack(FString InString)
	{
		FNPCSpawnBuffInfo BuffInfo;
		FString BuffID;
		FString ParamsStr;
		InString.Split("(", &BuffID, &ParamsStr);
		FString BuffStack;
		ParamsStr.Split(")", &BuffStack, &ParamsStr);
		BuffInfo.Id = BuffID;
		BuffInfo.AddStack = FCString::Atoi(*BuffStack);
		return BuffInfo;
	};
};

USTRUCT(BlueprintType)
struct FNPCTimerSpawnModel
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FNpcInfo NpcInfo;
	//当生成NPC后存在GameState里TMap CharacterList的Tag值(一般来说填的是触发TriggerCatcher的Id)
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Tag = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float TimerInterval = 0;

	float LastTriggeTime = 0;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> CreateCondition;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int MaxTriggeNum = 0;
	
	int CurTriggeNum = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> RemoveCondition;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> AIClips;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FNPCSpawnBuffInfo> Buffs;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnCreate;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PathNodeQueueId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int PathNodeQueueIndex = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwQuestComponentData QuestData;

	static FNPCSpawnBuffInfo SplitBuffIDAndStack(FString InString)
	{
		FNPCSpawnBuffInfo BuffInfo;
		FString BuffID;
		FString ParamsStr;
		InString.Split("(", &BuffID, &ParamsStr);
		FString BuffStack;
		ParamsStr.Split(")", &BuffStack, &ParamsStr);
		BuffInfo.Id = BuffID;
		BuffInfo.AddStack = FCString::Atoi(*BuffStack);
		return BuffInfo;
	};
};

USTRUCT(BlueprintType)
struct FNPCSpawnInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FNpcInfo NpcInfo;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Tag;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> AIClips;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FNPCSpawnBuffInfo> Buffs;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FJsonFuncData> OnCreate;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FTransform SpawnTrans = FTransform();
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PathNodeQueueId = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int PathNodeQueueIndex = 0;
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FAwQuestComponentData QuestData;
};

//怪物出生点
UCLASS(hideCategories=(Rendering, Physics, LOD, Activation, Input, Actor, Cooking, Collision))
class THEAWAKENER_FO_API ASpawnPoint : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	ASpawnPoint();

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	class USceneComponent* SceneComponent;
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	class UArrowComponent* ArrowComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FNPCSpawnModel> NPCSpawnInfoList;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FNPCTimerSpawnModel> NPCTimerSpawnInfoList;

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	void SetTriggerToTriggerManager();
	

};

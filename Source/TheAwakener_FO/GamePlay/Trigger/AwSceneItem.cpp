// Fill out your copyright notice in the Description page of Project Settings.


#include "AwSceneItem.h"
#include "Engine/StaticMesh.h" 
#include "Camera/CameraShakeBase.h"
#include "Components/StaticMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/GamePlay/Battle/OffenseManager.h"
#include "UObject/ConstructorHelpers.h"

// Sets default values
AAwSceneItem::AAwSceneItem()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	SceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("ScenceCompomemt"));
	SceneComponent->SetupAttachment(GetRootComponent());
	//CreateMesh
	Mesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("Mesh"));
	Mesh->SetupAttachment(SceneComponent);
	Mesh->SetIsReplicated(true);
	ConstructorHelpers::FObjectFinder<UStaticMesh> MeshComponentAsset(TEXT("/Game/ArtResource/Shapes/Shape_Sphere.Shape_Sphere"));
	Mesh->SetStaticMesh(MeshComponentAsset.Object);
	Mesh->SetRelativeScale3D(FVector(1, 1, 1));
	//Mesh Collision
	Mesh->SetGenerateOverlapEvents(true);
	Mesh->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	Mesh->SetCollisionObjectType(ECollisionChannel::ECC_GameTraceChannel4);//Terrain
	Mesh->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Block);

	AttackHitManager = this->CreateDefaultSubobject<UAttackHitManager>(TEXT("AttackHitManager"));

	bReplicates = true;
}

// Called when the game starts or when spawned
void AAwSceneItem::BeginPlay()
{
	Super::BeginPlay();

	bool CanSwitch = true;
	for (const FJsonFuncData SwitchFunc : SwitchFuncList)
	{
		UFunction* Func = UCallFuncLib::GetUFunction(SwitchFunc.ClassPath, SwitchFunc.FunctionName);
		if (Func)
		{
			struct
			{
				TArray<FString> Params;
				bool Result;
			}FuncParam;
			FuncParam.Params = SwitchFunc.Params;
			this->ProcessEvent(Func, &FuncParam);
			if (!FuncParam.Result)
			{
				CanSwitch = false;
				break;
			}
		}
	}

	if (!CanSwitch)
	{
		SetActorHiddenInGame(!CanSwitch);
		SetActorEnableCollision(CanSwitch);
		return;
	}

	if (BeProtectSwitchFuncList.Num() > 0)
	{
		bool WillBeProtect = true;
		for (const FJsonFuncData SwitchFunc : BeProtectSwitchFuncList)
		{
			UFunction* Func = UCallFuncLib::GetUFunction(SwitchFunc.ClassPath, SwitchFunc.FunctionName);
			if (Func)
			{
				struct
				{
					TArray<FString> Params;
					bool Result;
				}FuncParam;
				FuncParam.Params = SwitchFunc.Params;
				this->ProcessEvent(Func, &FuncParam);
				if (!FuncParam.Result)
				{
					WillBeProtect = false;
					break;
				}
			}
		}
		SetBeProtectEnable(WillBeProtect);
	}
	
	OriginPosition = this->GetActorLocation();
	UniqueId = FString::FromInt(this->GetUniqueID());
	if(!UGameplayFuncLib::GetAwGameState()->SceneItemList.Contains(UniqueId))
	{
		UGameplayFuncLib::GetAwGameState()->SceneItemList.Add(UniqueId, this);
	}
	
	InitProtector();
}

void AAwSceneItem::Init(FSceneItemModel Model, int ItemSide)
{
	Id = Model.Id;
	Tag = Model.Tag;
	SceneItemLifeSpan = Model.LifeSpan;
	TweenFunc = Model.Tween;
	Side = ItemSide;
	//PartProps = Model.Part;
	//GatherHitBoxes();
	if(!Model.Scale.Equals(FVector::ZeroVector))
		SceneComponent->SetRelativeScale3D(Model.Scale);
}

// Called every frame
void AAwSceneItem::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	if (AttackHitManager && Killed == false)
	{
		AttackHitManager->Update(DeltaTime);
		CheckForHit();
	}
	DurationTime += DeltaTime;
	//Check Lifespan
	if (SceneItemLifeSpan > 0)
	{
		RemainingTime = SceneItemLifeSpan - DurationTime;
		if (RemainingTime <= 0)
		{
			if(!IsValid(this))
				Destroy();
			return;
		}
	}
	if (DurationToDisappear > 0 && DurationTime >= DurationToDisappear && !IsPlayedDisappear)
	{
		IsPlayedDisappear = true;
		PlayDisappearEffect();
	}
	//Tween
	if (Killed == false && TweenFuncClassPath != "" && TweenFuncName != "")
	{
		UFunction* Func = UCallFuncLib::GetUFunction(TweenFuncClassPath, TweenFuncName);
		if (Func)
		{
			struct
			{
				FVector OriginPosition;
				float TimeElapsed;
				TArray<FString> Params;
				FVector Result;
			}TweenFuncParam;
			TweenFuncParam.TimeElapsed = DurationTime;
			TweenFuncParam.OriginPosition = OriginPosition;
			TweenFuncParam.Params = TweenFunc.Params;
			this->ProcessEvent(Func, &TweenFuncParam);
			if(TweenFuncParam.Result != FVector(0,0,0))
				this->SetActorLocation(TweenFuncParam.Result);
		}
	}
}

void AAwSceneItem::CheckForHit()
{
	TArray<FBeCaughtActorInfo> CaughtActors = AttackHitManager->ThisTickValidTarget(false, true, true);
	for (FBeCaughtActorInfo CaughtActor : CaughtActors)
	{
		AActor* Target = CaughtActor.BeCaughtActor;
		if(GetOwner() && Target == GetOwner()) break;
		if(AttackOffenseInfo.Contains(CaughtActor.AttackBoxName))
		{
			const FOffenseInfo OffenseInfo = AttackOffenseInfo[CaughtActor.AttackBoxName];
			if(AttackHitManager->TargetCanBeHitByHitRecord(Target, OffenseInfo.SourceId, OffenseInfo.Index))
			{
				UOffenseManager::DoOffense(AttackHitManager,OffenseInfo,CaughtActor,nullptr,false);
			}
		}
	}
}

void AAwSceneItem::Kill(bool ByDamage)
{
	if (!bDestroyed)
	{
		bDestroyed = true;
		this->BeforeKill(ByDamage);
		this->OnItemDistoryDelegate.Broadcast(this);

		if(ByDamage)
		{
			UGameplayFuncLib::GetTriggerManager()->AddSceneItemDestroy(this);

			if (this->OnKillSwitchKey.IsEmpty() == false)
			{
				UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
				if(GameInstance)
				{
					GameInstance->RoleInfo.SetSwitchValue(OnKillSwitchKey, OnKillSwitchValue);
				}
			}
			if(OnKillCampDungeonId.IsEmpty() == false && OnKillCampId.IsEmpty() == false)
			{
				UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
				if(GameInstance)
				{
					GameInstance->CampProgressModify(OnKillCampDungeonId, OnKillCampId, OnKillCampValue);
				}
			}
			
			//OnKillFunc
			for (FJsonFuncData KillFunc : OnKillFuncList)
			{
				if (KillFunc.ClassPath != "" && KillFunc.FunctionName != "")
				{
					UFunction* Func;
					Func = UCallFuncLib::GetUFunction(KillFunc.ClassPath, KillFunc.FunctionName);
					if (Func)
					{
						struct
						{
							AAwSceneItem* SceneItem;
							TArray<FString> Params;
							UTimelineNode* Result = nullptr;
						}OnKillFuncParam;
						OnKillFuncParam.SceneItem = this;
						OnKillFuncParam.Params = KillFunc.Params;
						this->ProcessEvent(Func, &OnKillFuncParam);
						if (OnKillFuncParam.Result)
						{
							Cast<UAwGameInstance>(GWorld->GetGameInstance())->TimelineManager->AddNode(OnKillFuncParam.Result);
						}
					}
				}
			}
		}

		if (ByDamage == true && BeKilledSound)
		{
			UGameplayFuncLib::PlaySFXatLocation(BeKilledSound, this->GetActorLocation(), this->GetActorRotation());
		}else if (ByDamage == false && DestroySound)
		{
			UGameplayFuncLib::PlaySFXatLocation(DestroySound, this->GetActorLocation(), this->GetActorRotation());
		}
		
		if (this->TimeToBeRemoved > 0)
		{
			SceneItemLifeSpan = this->TimeToBeRemoved + DurationTime;
			DurationToDisappear = this->TimeToDisappear + DurationTime;
		}
		else
			Destroy();
	}
}

void AAwSceneItem::Pause_Implementation()
{
	this->Paused = true;
}

void AAwSceneItem::Resume_Implementation()
{
	this->Paused = false;
}

void AAwSceneItem::InitProtector()
{
	if (!bIsBeProtect)
		return;
	
	for (FProtectorSpawnModel ProtectorModel : ProtectorSpawnModels)
	{
		FNPCSpawnInfo SpawnInfo = FNPCSpawnInfo();
		SpawnInfo.NpcInfo = ProtectorModel.SpawnModel.NpcInfo;
		SpawnInfo.Tag = ProtectorModel.SpawnModel.Tag;
		SpawnInfo.Buffs = ProtectorModel.SpawnModel.Buffs;
		SpawnInfo.AIClips = ProtectorModel.SpawnModel.AIClips;
		SpawnInfo.OnCreate = ProtectorModel.SpawnModel.OnCreate;
		SpawnInfo.PathNodeQueueId = ProtectorModel.SpawnModel.PathNodeQueueId;
		SpawnInfo.PathNodeQueueIndex = ProtectorModel.SpawnModel.PathNodeQueueIndex;
		FTransform Transform = ProtectorModel.SpawnPoint ? ProtectorModel.SpawnPoint->GetActorTransform() :
			this->GetActorTransform();
		Transform.SetScale3D(FVector::OneVector);
		SpawnInfo.SpawnTrans = Transform;

		AAwCharacter* NewProtector = UGameplayFuncLib::CreateCharacterBySpawnInfo(SpawnInfo, SpawnInfo.Tag);

		if (NewProtector)
		{
			NewProtector->GetReady();
			Protectors.Add(NewProtector);
		}

		SetProtectorsEnable(IsProtectorEnable);
	}
	
	OnProtectorSpawnFinish();
}

void AAwSceneItem::RemoveProtector(AAwCharacter* Protector)
{
	Protectors.Remove(Protector);
	if (Protectors.Num() <= 0)
		SetBeProtectEnable(false);
}

void AAwSceneItem::SetBeProtectEnable(bool Enable)
{
	if (bIsBeProtect && !Enable)
	{
		bIsBeProtect = false;
		OnBeProtectDisable();
	}
	if (!bIsBeProtect && Enable)
	{
		bIsBeProtect = true;
		OnBeProtectEnable();
	}
}

void AAwSceneItem::SetProtectorsEnable(bool Enable)
{
	const FString BuffId = "ProtectSceneItem";
	if (Enable)
	{
		for (AAwCharacter* Protector : Protectors)
		{
			const FBuffModel BuffModel = UGameplayFuncLib::GetAwDataManager()->GetBuffModelById(BuffId);
			const FAddBuffInfo AddBuffInfo = FAddBuffInfo(nullptr, Protector, BuffModel, 1, 0, false, true);
			FBuffObj* Buff = Protector->AddBuff(AddBuffInfo);
			Buff->Param.Add("SceneItemId",UniqueId);
		}
	}
	else
	{
		for (AAwCharacter* Protector : Protectors)
			Protector->RemoveBuffById(BuffId);
	}
}

FOffendedCaughtResult AAwSceneItem::CanBeOffended(
		FOffenseInfo OffenseInfo,
		UAttackHitManager* Attacker,
		AAwCharacter* AttackerInCharge,
		bool IncludeAttackerActionHitBoxes,
		bool SameSideFriendlyFire,
		bool AllyFriendlyFire
	)
{
	if (this->Killed) return FOffendedCaughtResult();
	if (!bOffenedActive) return FOffendedCaughtResult(OffenseInfo);
	if (!Attacker) return FOffendedCaughtResult(OffenseInfo);
	if(AttackerInCharge && AttackerInCharge->Side == Side) return FOffendedCaughtResult();

	//对方攻击框和记录信息判断
	TArray<FOffenseInfo> OInfos;
	OInfos.Add(OffenseInfo);
	TArray<FOffendedCaughtResult> CaughtInfos = Attacker->GetThisTickTargetCaughtInfo(this, OInfos);

	FOffendedCaughtResult Res;
	for (const FOffendedCaughtResult CInfo : CaughtInfos)
	{
		if (CInfo.Hit == true && Res.Hit == false)
		{
			Res = CInfo;
			continue;
		}
		if (
			CInfo.BeCaughtActorInfo.CaughtHitBoxData &&
			(!Res.BeCaughtActorInfo.CaughtHitBoxData || Res.BeCaughtActorInfo.CaughtHitBoxData->Priority < CInfo.BeCaughtActorInfo.CaughtHitBoxData->Priority)
		)
		{
			Res = CInfo;
			continue;
		}
	}

	return Res;
}

 void AAwSceneItem::BeOffended(
	FOffenseInfo OffenseInfo,
	UAttackHitManager* Attacker,
	AAwCharacter* AttackerInCharge,
	USceneComponent* BeHitBox,
	USceneComponent* FromAttackBox,
	bool SameSideFriendlyFire, bool AllyFriendlyFire
){
	if (this->Killed) return;
	const UBeCaughtHitBox* HitBox = BeHitBox ? BeHitBox->GetAssetUserData<UBeCaughtHitBox>() : nullptr;
	
	//如果攻击信息启动，说明有攻击，就可能造成动作变化和伤害
	if (OffenseInfo.AttackInfo.Active)
	{
		FVector HitLocation = BeHitBox ? BeHitBox->GetComponentLocation() : FVector::ZeroVector;
		FVector HitLocOffset;
		FVector HitNormal = BeHitBox ? BeHitBox->GetComponentRotation().Vector() : FVector::ZeroVector;
		if (FromAttackBox && HitBox)
		{
			UGameplayFuncLib::GetHitResultBetweenSceneComponent(BeHitBox, FromAttackBox, HitLocation, HitNormal);
		}
		HitLocOffset = HitLocation;
		if (BeHitBox) HitLocOffset -= BeHitBox->GetComponentLocation();
		
		//伤害处理
		FDefenseInfo DefInfo = BeHitBox && this->SpecDefenseInfo.Contains(BeHitBox->GetName()) ? SpecDefenseInfo[BeHitBox->GetName()] : DefaultDefenseInfo;
		// 是否是保护的
		if (bIsBeProtect) DefInfo = ProtectDefenseInfo;
		FDamageInfo DInfo = FDamageInfo::FromAttackAndDefense(
			BeHitBox, OffenseInfo.AttackInfo, DefInfo, HitLocOffset
		);
		DInfo.Attacker = AttackerInCharge;
		//UDamageManager::AddDamage(AttackerInCharge, this, DInfo);

		//为攻击方开启临时的Cancel点
		if (AttackerInCharge)
		{
			for (FCancelTagInfo TempCancelPoint : DInfo.AttackerActionChange.TemporaryCancelPoints)
			{
				AttackerInCharge->AddCancelTag(ECancelTagType::Temporary, TempCancelPoint.CancelPointIndex, TempCancelPoint.Duration);
			}
		}

		//改变攻击方的动作，是否会落马等，由对应动作中的AnimNotify来执行
		if (AttackerInCharge)
		{
			if (DInfo.AttackerActionChange.HitStun.Active)
			{
				FForceMoveInfo HitStun = DInfo.AttackerActionChange.HitStun;
				HitStun.Velocity *= AttackerInCharge->CharacterObj.CurProperty.BeStrikeRate;
				HitStun.InSec *= AttackerInCharge->CharacterObj.CurProperty.BeStrikeRate;
				if (!HitStun.Velocity.IsNearlyZero() && HitStun.InSec > 0)
				{
					AttackerInCharge->LastIncomingForce = HitStun.Velocity;
				}
				DInfo.AttackerActionChange.HitStun = HitStun;
			}
			for (FString ABeep : DInfo.AttackerActionChange.AchievementBeep)
			{
				AttackerInCharge->AchievementSignalBeep(ABeep);
			}
			FActionParam AttackerAP = FActionParam();
			AttackerAP.Active = true;
			AttackerAP.PriorityDistance = OffenseInfo.AttackInfo.AttackerActionChange.Priority - DInfo.AttackerActionChange.Priority;
			AttackerInCharge->PreorderActionByActionChangeInfo(DInfo.AttackerActionChange, AttackerAP);
		}
		
		//震屏，只有攻击方是玩家的才会
		if (AttackerInCharge && UGameplayFuncLib::GetAwGameState() && UGameplayFuncLib::GetAwGameState()->GetMyCharacter() == AttackerInCharge)
		{
			if (OffenseInfo.AttackInfo.ShakeInfo.Shake && this->GetWorld())
			{
				//只有用了防守方的DefenseInfo，才会播放震动（因为震动是DefenseInfo的数据嘛）
				const FVector FinalPos = OffenseInfo.AttackInfo.ShakeInfo.Offset + this->GetActorLocation();
				APlayerCameraManager::PlayWorldCameraShake(
					this->GetWorld(),
					OffenseInfo.AttackInfo.ShakeInfo.Shake, FinalPos,
					OffenseInfo.AttackInfo.ShakeInfo.FullShockRange,
					OffenseInfo.AttackInfo.ShakeInfo.LoseShockRange,
					OffenseInfo.AttackInfo.ShakeInfo.FallOff,
					OffenseInfo.AttackInfo.ShakeInfo.DoRotate
				);
			}
		}
		
		//播放对应的特效（TODO：还未确定怎么做），在HitBox播放受击方的特效
		FTransform HitTrans = BeHitBox ? BeHitBox->GetComponentTransform() : this->GetActorTransform();
		HitTrans.SetLocation(HitLocation);
		HitTrans.SetRotation(HitNormal.ToOrientationQuat());

		if (UGameplayFuncLib::GetHitVFX(DInfo.AttackerActionChange) && HitBox)
			UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(DInfo.AttackerActionChange), HitTrans, true, true);
		if (UGameplayFuncLib::GetHitSFX(DInfo.AttackerActionChange) && HitBox)
			UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(DInfo.AttackerActionChange), HitLocation, HitNormal.Rotation());
		if(SpecDefenseInfo.Num())
		{
			if(this->SpecDefenseInfo.Contains(BeHitBox->GetName()))
			{
				if (UGameplayFuncLib::GetHitVFX(SpecDefenseInfo[BeHitBox->GetName()].DefenderActionChange) && HitBox)
					UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(SpecDefenseInfo[BeHitBox->GetName()].DefenderActionChange), HitTrans, true, true);
				if (UGameplayFuncLib::GetHitSFX(SpecDefenseInfo[BeHitBox->GetName()].DefenderActionChange) && HitBox)
					UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(SpecDefenseInfo[BeHitBox->GetName()].DefenderActionChange), HitLocation, HitNormal.Rotation());
			}
		}
		else
		{
			if (UGameplayFuncLib::GetHitVFX(DefaultDefenseInfo.DefenderActionChange) && HitBox)
				UGameplayFuncLib::CreateVFXatLocation(UGameplayFuncLib::GetHitVFX(DefaultDefenseInfo.DefenderActionChange), HitTrans, true, true);
			if (UGameplayFuncLib::GetHitSFX(DefaultDefenseInfo.DefenderActionChange) && HitBox)
				UGameplayFuncLib::PlaySFXatLocation(UGameplayFuncLib::GetHitSFX(DefaultDefenseInfo.DefenderActionChange), HitLocation, HitNormal.Rotation());
		}
		if (bIsBeProtect)
			OnHurtWithProtect();
		BeDamaged(DInfo, Attacker, FromAttackBox);
	}
	
	if (Attacker)
	{
		Attacker->AddHitRecord(
			FOffenseHitRecord(
				OffenseInfo.SourceId, 
				OffenseInfo.Index,
				this,
				OffenseInfo.CanHitTimes - 1,
				OffenseInfo.HitSameTargetDelay
			)
		);
	}
}

void AAwSceneItem::BeDamaged(FDamageInfo DamageInfo, UAttackHitManager* Attacker, USceneComponent* FromAttackBox)
{
	if (this->ChaResource.HP <= 0) return;
	const int ToDealDamage = this->IsDamage1Type ? 1:DamageInfo.FinalDamage();
	if (this->bIsBeProtect)
	{
		DamageInfo.DamagePower.SetDamageZero();
	}
	else
	{
		this->ChaResource.HP -= ToDealDamage;

		//跳数字
		if(UGameplayFuncLib::GetAwDataManager()->DebugConfig.ShowPopText)
		{
			const FString TextId = "SceneItemDamaged";
	
			if (UGameplayFuncLib::GetAwDataManager()->DebugConfig.PopTextInfo.Contains(TextId))
			{
				const FPopTextLauncher TextLauncher = UGameplayFuncLib::GetAwDataManager()->DebugConfig.PopTextInfo[TextId];
				const int PopPriority = TextLauncher.Priority;
				const int PopSize = TextLauncher.Size;
				const FLinearColor PopColor = FLinearColor::FromSRGBColor(TextLauncher.Color);
				FVector PopPos = DamageInfo.HitBox ? DamageInfo.HitBox->GetComponentLocation() : this->GetActorLocation();
				PopPos += DamageInfo.HitLocationOffset;
				UGameplayFuncLib::GetAwGameInstance()->PopText(
					PopPos, FString::FromInt(ToDealDamage), PopPriority, PopSize, PopColor
				);
			}
		}
	}

	//OnHitFunc
	for (FJsonFuncData HitFunc : OnHitFuncList)
	{
		if (HitFunc.ClassPath != "" && HitFunc.FunctionName != "")
		{
			UFunction* Func;
			Func = UCallFuncLib::GetUFunction(HitFunc.ClassPath, HitFunc.FunctionName);
			if (Func)
			{
				struct
				{
					FDamageInfo DamageInfo;
					AAwSceneItem* Defender;
					TArray<FString> Params;
					bool Result;
				}OnHitFuncParam;
				OnHitFuncParam.DamageInfo = DamageInfo;
				OnHitFuncParam.Defender = this;
				OnHitFuncParam.Params = HitFunc.Params;
				this->ProcessEvent(Func, &OnHitFuncParam);
			}
		}
	}
	OnBeHit(DamageInfo, Attacker, FromAttackBox);
	
	if (this->ChaResource.HP <= 0)
	{
		this->Kill(true);
	}
}

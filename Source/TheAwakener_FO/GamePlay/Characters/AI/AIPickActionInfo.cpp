// Fill out your copyright notice in the Description page of Project Settings.


#include "AIPickActionInfo.h"

#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

FAIPickActionData FAIPickActionData::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAIPickActionData Data = FAIPickActionData();
	Data.ActionId = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("MinRange"))
	{
		Data.MinRange.Add(Arr->AsNumber());
	}
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("MaxRange"))
	{
		Data.MaxRange.Add(Arr->AsNumber());
	}
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("Rate"))
	{
		Data.PickRate.Add(Arr->AsNumber());
	}
	Data.EnemyInFrontRate = UDataFuncLib::AwGetNumberField(JsonObj, "Front", 0.000f);
	Data.EnemyInBackRate = UDataFuncLib::AwGetNumberField(JsonObj, "Back", 0.000f);

	return Data;
}

FAIPickActionInfo FAIPickActionInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FAIPickActionInfo Info = FAIPickActionInfo();
	Info.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("RestRate"))
	{
		Info.RestRate.Add(Arr->AsNumber());
	}
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("Actions"))
	{
		Info.Actions.Add(FAIPickActionData::FromJson(Arr->AsObject()));
	}
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("RestActions"))
	{
		Info.RestActions.Add(FAIPickActionData::FromJson(Arr->AsObject()));
	}
	return Info;
}

FRogueAIRangeWeightInfo FRogueAIRangeWeightInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueAIRangeWeightInfo Info = FRogueAIRangeWeightInfo();
	
	Info.MinRange = UDataFuncLib::AwGetNumberField(JsonObj, "MinRange",0.0f);
	Info.MaxRange = UDataFuncLib::AwGetNumberField(JsonObj, "MaxRange",0.0f);
	Info.Weight = UDataFuncLib::AwGetNumberField(JsonObj, "Weight",0);
	
	return Info;
}

FRogueAICheckBuffInfo FRogueAICheckBuffInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueAICheckBuffInfo Info = FRogueAICheckBuffInfo();
	
	Info.BuffId = UDataFuncLib::AwGetStringField(JsonObj, "BuffId","");
	Info.BuffStack = UDataFuncLib::AwGetNumberField(JsonObj, "BuffStack",0);
	Info.Weight = UDataFuncLib::AwGetNumberField(JsonObj, "Weight",0);

	return Info;
}

FRogueAISwitchWeightInfo FRogueAISwitchWeightInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueAISwitchWeightInfo Info = FRogueAISwitchWeightInfo();

	Info.SwitchId = UDataFuncLib::AwGetStringField(JsonObj, "SwitchId","");
	Info.MinValue = UDataFuncLib::AwGetNumberField(JsonObj, "MinRange",0.0f);
	Info.MaxValue = UDataFuncLib::AwGetNumberField(JsonObj, "MaxRange",0.0f);
	Info.Weight = UDataFuncLib::AwGetNumberField(JsonObj, "Weight",0);
	
	return Info;
}

FRogueAIPickActionData FRogueAIPickActionData::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueAIPickActionData Data = FRogueAIPickActionData();
	
	Data.ActionId = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Data.BaseWeight = UDataFuncLib::AwGetNumberField(JsonObj, "BaseWeight",1);
	
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("DistanceWeight"))
	{
		Data.DistanceWeightList.Add(FRogueAIRangeWeightInfo::FromJson(Arr->AsObject()));
	}
	
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("HPWeight"))
	{
		Data.HPWeightList.Add(FRogueAIRangeWeightInfo::FromJson(Arr->AsObject()));
	}

	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("BuffWeight"))
	{
		Data.BuffWeightList.Add(FRogueAICheckBuffInfo::FromJson(Arr->AsObject()));
	}

	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("DegreeWeight"))
	{
		Data.TargetInDegreeWeight.Add(FRogueAIRangeWeightInfo::FromJson(Arr->AsObject()));
	}

	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("SwitchWeight"))
	{
		Data.SwitchWeight.Add(FRogueAISwitchWeightInfo::FromJson(Arr->AsObject()));
	}

	Data.TargetOnGroundWeight = UDataFuncLib::AwGetNumberField(JsonObj, "OnGround", 0.000f);
	Data.TargetInAirWeight = UDataFuncLib::AwGetNumberField(JsonObj, "InAir", 0.000f);

	Data.TargetInDodgeWeight = UDataFuncLib::AwGetNumberField(JsonObj, "InDodge", 0.000f);
	
	Data.TargetInHurtWeight = UDataFuncLib::AwGetNumberField(JsonObj, "InHurt", 0.000f);

	Data.bClearedGameWeight = UDataFuncLib::AwGetNumberField(JsonObj, "ClearedGame", 0.000f);

	Data.bOutofCameraWeight = UDataFuncLib::AwGetNumberField(JsonObj, "OutofCamera", 0.000f);

	Data.MinActionCD = UDataFuncLib::AwGetNumberField(JsonObj, "MinActionCD", 0.000f);

	Data.MaxActionCD = UDataFuncLib::AwGetNumberField(JsonObj, "MaxActionCD", 0.000f);

	Data.bWaitAction = UDataFuncLib::AwGetBoolField(JsonObj, "WaitAction", false);

	return Data;
}

FRogueAIPickActionInfo FRogueAIPickActionInfo::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FRogueAIPickActionInfo Info = FRogueAIPickActionInfo();
	Info.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	
	for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Arr : JsonObj->GetArrayField("Actions"))
	{
		Info.Actions.Add(FRogueAIPickActionData::FromJson(Arr->AsObject()));
	}
	
	return Info;
}

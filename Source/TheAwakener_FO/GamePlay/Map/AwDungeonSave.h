// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DungeonMap.h"
#include "AwDungeonSave.generated.h"

/**
 * 
 */
USTRUCT(BlueprintType)
struct FCampEventRecord
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadOnly)
	FString EventId = "";
	UPROPERTY(BlueprintReadOnly)
	int TriggerTime = 0;
};

USTRUCT(BlueprintType)
struct FAwDungeonCampSave
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	FString CampId = "";
	UPROPERTY(BlueprintReadWrite)
	int CampProgress = 0;
	UPROPERTY(BlueprintReadWrite)
	TArray<FCampEventRecord> EventRecordList;

	UPROPERTY(BlueprintReadWrite)
	float LastCampProgress = 0;
};

USTRUCT(BlueprintType)
struct FAwDungeonSave
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	FString DungeonId = "";
	UPROPERTY(BlueprintReadWrite)
	TArray<FAwDungeonCampSave> Camps;
	UPROPERTY(BlueprintReadOnly)
	TArray<FDungeonTile> RoomList;
	UPROPERTY(BlueprintReadOnly)
	TArray<FDungeonRoadInfo> RoadList;
};
// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "DragBarUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UDragBarUI : public UBaseUI
{
	GENERATED_BODY()


	UPROPERTY()
	float OldMousePositionX = 0.0;

	UPROPERTY()
	float CurMousePositionX = 0.0;

	UPROPERTY()
	float DifferenceValue = 0.0;

	UPROPERTY()
	bool ButtonIsDown = false;

	FScriptDelegate ButtonDownDelegate;

	FScriptDelegate ButtonUpDelegate;
	

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UProgressBar* Drag_ProgressBar;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UButton* Drag_BT;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* DragBarValueTextBlock;

	UFUNCTION()
	void ButtonDown();
	
	UFUNCTION()
	void ButtonUp();
	
	UFUNCTION(BlueprintCallable)
	float GetDragBarValue();
	
	UFUNCTION(BlueprintCallable)
	void SetDragBarValue(float Value);

	UFUNCTION(BlueprintCallable)
	void UpdateDragBarValue(float Value);

	UFUNCTION()
	bool GetButtonIsDown() const {return ButtonIsDown;}

	UFUNCTION()
	void SetButtonIsDown(bool IsDown){ButtonIsDown = IsDown;}

	UFUNCTION()
	void MouseControl();

	UFUNCTION()
	void GamepadControl();


	UFUNCTION()
	void SetDifferenceValue(float Value);

	UFUNCTION()
	void FocusDrag_BT();

	UFUNCTION()
	void NotFocusDrag_BT();
};


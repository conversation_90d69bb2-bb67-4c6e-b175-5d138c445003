// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DragBarUI.h"
#include "SwitchoverUI.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "SettingsUI.generated.h"

/**
 * 
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FChangeFocusIndex,int,Index);
UCLASS()
class THEAWAKENER_FO_API USettingsUI : public UBaseUI
{
	GENERATED_BODY()

	UPROPERTY()
	bool IsFocus;
	
	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	virtual void NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
	virtual void NativeOnMouseLeave(const FPointerEvent& InMouseEvent) override;

public:

	UPROPERTY(BlueprintAssignable,Category = "Events")
	FChangeFocusIndex ChangeFocusIndex;
	
	
	UPROPERTY()
	int Index = 0;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* SettingsName_TextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UDragBarUI* DragBarUI;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	USwitchoverUI* SwitchoverUI;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* SelectImage;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* SelectImage1;
	
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString Tag;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	bool IsDragBar;


	UFUNCTION()
	void SetIsFocus(bool Focus){ IsFocus = Focus;}

	UFUNCTION()
	bool GetIsFocus() const { return IsFocus;}
	
	UFUNCTION()
	bool GetIsDragBar() const {return IsDragBar;}

	UFUNCTION(BlueprintCallable)
	void CurFocusChange();

	UFUNCTION(BlueprintCallable)
	void CurFocusChangeUpdateView();

	UFUNCTION(BlueprintCallable)
	void LastFocusChange();
	
	UFUNCTION()
	void UpdateView();

	UFUNCTION()
	void RefreshUI();
};

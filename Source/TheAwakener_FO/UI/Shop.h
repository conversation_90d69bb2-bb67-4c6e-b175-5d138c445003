// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/Button.h"
#include "Components/TileView.h"
#include "Menu/ItemHint.h"
#include "TheAwakener_FO/GamePlay/Role/AwRoleInfo.h"
#include "TheAwakener_FO/GamePlay/Trading/Trading.h"
#include "Thing/PriceView.h"
#include "Shop.generated.h"

/**
 * 商店
 */
UCLASS(BlueprintType)
class THEAWAKENER_FO_API UShop : public UUserWidget
{
	GENERATED_BODY()
private:
	UPROPERTY()
	UTileView* SellingItemList;
	UPROPERTY()
	UButton* QuitBtn;
	UPROPERTY()
	UPriceView* MyCurrencyView;
	UPROPERTY()
	UItemHint* ItemHint;
	
	virtual void NativeOnInitialized() override;

	FAwRoleInfo* Role;

	UPROPERTY()
	FDeal SelectedDeal = FDeal();

	//virtual FReply NativeOnMouseButtonDoubleClick(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent) override;
public:
	UPROPERTY()
	FTrading Trading;

	UFUNCTION(BlueprintCallable)
	void Set(FTrading ShopInfo);

	/**
	 * TODO: Control
	 * 光标移动上去，以及如果已经在上面按下确定按钮
	 */
	UFUNCTION()
	void ItemSelected(FDeal Deal);

	//买进某个交易品，返回买没买成
	UFUNCTION()
	bool Buy(FDeal Deal);

	UFUNCTION()
	bool Enough(FDeal Deal);

	//关闭
	UFUNCTION()
	void CloseMe();

	UFUNCTION()
	void Draw();

	void SetRole(FAwRoleInfo* BuyerRole);	
};

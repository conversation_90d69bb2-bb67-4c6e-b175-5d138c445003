// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "TestUIButton.generated.h"

class UButton;
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UTestUIButton : public UUserWidget
{
	GENERATED_BODY()
	virtual void NativeOnInitialized() override;
	void ChangeToClass(FString ClassId);
public:
	//更换职业按钮
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* ClassToggleButton;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* ToWarriorBtn;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* ToDualBladeBtn;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* ToLancerBtn;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* ToPieceHandSwordBtn;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* AddImmuneBuffBtn;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UTextBlock* TextBlock_Immune;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* AddBuff_Damage999;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UTextBlock* TextBlock_Damage999;

	UFUNCTION()
	void ClassToggleBtnClick();
	UFUNCTION()
	void ToBigSword();
	UFUNCTION()
	void ToDualBlade();
	UFUNCTION()
	void ToLance();
	UFUNCTION()
	void ToPieceHandSword();
	UFUNCTION()
	void AddImmuneBuff();
	UFUNCTION()
	void AddPowerUpBuff();

	//暂停测试
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UButton* PauseResumeButton;

	UFUNCTION()
	void PauseResumeButtonBtnClick();
};

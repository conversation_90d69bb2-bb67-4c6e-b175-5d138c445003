// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "FileMediaSource.h"
#include "MediaPlayer.h"
#include "Blueprint/UserWidget.h"
#include "MediaPlayerUI.generated.h"

/**
 * 
 */


DECLARE_DYNAMIC_MULTICAST_DELEGATE(FMediaEndDelegate);
UCLASS()
class THEAWAKENER_FO_API UMediaPlayerUI : public UUserWidget
{
	GENERATED_BODY()

	
	UPROPERTY()
	bool ShowBackWhenEnd;
	UPROPERTY()
	bool InPlay = false;


	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
public:

	UPROPERTY(BlueprintAssignable)
	FMediaEndDelegate MediaEndDelegate;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UMediaPlayer* MediaPlayer;

	
	UFUNCTION(BlueprintCallable, Category = "Character")
	void StartPlayMedia(UFileMediaSource* MediaSource,bool bShowBackWhenEnd,bool bLoop = false);

	UFUNCTION(BlueprintCallable)
	bool Playing(){return InPlay;}

	UFUNCTION()
	void MediaPlayerEnd();
};

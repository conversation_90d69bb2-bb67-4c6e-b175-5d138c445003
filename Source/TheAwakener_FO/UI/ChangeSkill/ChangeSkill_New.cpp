// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeSkill_New.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UChangeSkill_New::SetUIInfo(int ButtonIndex)
{
	CurActionSelection = ActionSelections[ButtonIndex];
	const FActionSelectionUIInfo ActionSelectionUIInfo = UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(CurActionSelection.Id);
	CurMainSkillUIInfos = ActionSelectionUIInfo.Actions;
	CurMainSkillId = Character->GetActionComponent()->CurrentSelectedMainActionId(CurActionSelection);

	CurActionLink = UGameplayFuncLib::GetAwDataManager()->GetActionLinkByMainActionId(CurMainSkillId);
	const FActionLinkUIInfo ActionLinkUIInfo = UGameplayFuncLib::GetAwDataManager()->GetActionLinkUIInfo(CurActionLink.Id);
	CurSubSkillUIInfos = ActionLinkUIInfo.CandidateActions;
	CurSubSkillId = Character->GetActionComponent()->CurrentLinkedSelectActionId(CurActionLink);
}

void UChangeSkill_New::ChangeSubSkill(FActionLink ActionLink, FString SelectedActionId, bool Save)
{
	UActionComponent* ActionComp = Character->GetActionComponent();
	const FActionInfo* ToAction = ActionComp->GetActionByOriginId(SelectedActionId);
	const FString ToId = ToAction ? ToAction->Id : SelectedActionId;
	
	ActionComp->ChangeLinkedAction(ActionLink, ToId, Save);
}

void UChangeSkill_New::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	if (UGameplayFuncLib::GetAwGameState())
		Character = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	if (Character->OwnerPlayerController)
		Character->OwnerPlayerController->GameControlState = EGameControlState::ChangeSkillOrClass;
	
	ActionSelections = UGameplayFuncLib::GetAwDataManager()->
					GetActionSelectionsByClassId(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->PlayerClassId);
	
	OnShow();
}

void UChangeSkill_New::BeginDestroy()
{
	Super::BeginDestroy();
}

void UChangeSkill_New::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	if (Character)
	{
		if (Character->IsActionOccur("ChangeSkillOrClass_Up", EAwInputState::Press, true))
		{
			switch (MyState)
			{
			case EChangeSkillState::Intro: break;
			case EChangeSkillState::OneLine: Up_OneLine(); break;
			case EChangeSkillState::TwoLine: Up_TwoLine(); break;
			case EChangeSkillState::ThreeLine: Up_ThreeLine(); break;
			case EChangeSkillState::Outro: break;
			default: break;
			}
		}
		if (Character->IsActionOccur("ChangeSkillOrClass_Down", EAwInputState::Press, true))
		{
			switch (MyState)
			{
			case EChangeSkillState::Intro: break;
			case EChangeSkillState::OneLine: Down_OneLine(); break;
			case EChangeSkillState::TwoLine: Down_TwoLine(); break;
			case EChangeSkillState::ThreeLine: Down_ThreeLine(); break;
			case EChangeSkillState::Outro: break;
			default: break;
			}
		}
		if (Character->IsActionOccur("ChangeSkillOrClass_Left", EAwInputState::Press, true))
		{
			switch (MyState)
			{
			case EChangeSkillState::Intro: break;
			case EChangeSkillState::OneLine: Left_OneLine(); break;
			case EChangeSkillState::TwoLine: Left_TwoLine(); break;
			case EChangeSkillState::ThreeLine: Left_ThreeLine(); break;
			case EChangeSkillState::Outro: break;
			default: break;
			}
		}
		if (Character->IsActionOccur("ChangeSkillOrClass_Right", EAwInputState::Press, true))
		{
			switch (MyState)
			{
			case EChangeSkillState::Intro: break;
			case EChangeSkillState::OneLine: Right_OneLine(); break;
			case EChangeSkillState::TwoLine: Right_TwoLine(); break;
			case EChangeSkillState::ThreeLine: Right_ThreeLine(); break;
			case EChangeSkillState::Outro: break;
			default: break;
			}
		}
		if (Character->IsActionOccur("ChangeSkillOrClass_Confirm", EAwInputState::Press, true))
		{
			switch (MyState)
			{
			case EChangeSkillState::Intro: break;
			case EChangeSkillState::OneLine: Confirm_OneLine(); break;
			case EChangeSkillState::TwoLine: Confirm_TwoLine(); break;
			case EChangeSkillState::ThreeLine: Confirm_ThreeLine(); break;
			case EChangeSkillState::Outro: break;
			default: break;
			}
		}
		if (Character->IsActionOccur("ChangeSkillOrClass_Refuse", EAwInputState::Press, true))
		{
			switch (MyState)
			{
			case EChangeSkillState::Intro: break;
			case EChangeSkillState::OneLine: Refuse_OneLine(); break;
			case EChangeSkillState::TwoLine: Refuse_TwoLine(); break;
			case EChangeSkillState::ThreeLine: Refuse_ThreeLine(); break;
			case EChangeSkillState::Outro: break;
			default: break;
			}
		}
	}
}
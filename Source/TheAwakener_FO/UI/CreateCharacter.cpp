// Fill out your copyright notice in the Description page of Project Settings.


#include "CreateCharacter.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UCreateCharacter::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	TitleText = Cast<UTextBlock>(GetWidgetFromName(TEXT("MainMenuTitle")));
	NameText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Name")));
	RaceText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Race")));
	GenderText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Gender")));
	ClassText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Class")));
	VoiceText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Voice")));
	AppearanceText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Appearance")));
	ElementText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Element")));
	ConfirmText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Confirm")));
	SelectedMenuText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Selected")));
	SelectedRaceText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_SelectRace")));
	RaceTitleText = Cast<UTextBlock>(GetWidgetFromName(TEXT("RaceDialogTitle")));
	RaceHumanText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_RaceHuman")));
	RaceElfText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_RaceElf")));
	RaceDwarfText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_RaceDwarf")));
	GenderTitleText = Cast<UTextBlock>(GetWidgetFromName(TEXT("GenderDialogTitle")));
	GenderMaleText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Gender_Male")));
	GenderFemaleText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Gender_Female")));
	SelectedGenderText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_SelectGender")));
	ClassTitleText = Cast<UTextBlock>(GetWidgetFromName(TEXT("ClassDialogTitle")));
	ClassWarriorText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Warrior")));
	ClassArcherText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Archer")));
	ClassMageText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_Wizard")));
	ClassSelectText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_SelectClass")));
	NameTitleText = Cast<UTextBlock>(GetWidgetFromName(TEXT("NameDialogTitle")));

	NameEditableText = Cast<UEditableText>(GetWidgetFromName(TEXT("NameInputText")));
	
	MainSelectionDialog = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("MainPanel")));
	RaceSelectionDialog = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("RaceSelection")));
	SelectMenuPanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("Select_Sign")));
	SelectRacePanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("SelectedRace")));
	SelectGenderDialog = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("GenderSelection")));
	SelectGenderPanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("SelectedGender")));
	SelectClassDialog = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("ClassSelection")));
	SelectClassPanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("SelectedClass")));
	InputNameDialog = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("NameInputArea")));
	
	if (TitleText) TitleText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Title")));
	if (NameText) NameText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Name")));
	if (RaceText) RaceText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Race")));
	if (GenderText) GenderText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Gender")));
	if (ClassText) ClassText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Class")));
	if (VoiceText) VoiceText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Voice")));
	if (AppearanceText) AppearanceText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Appearance")));
	if (ElementText) ElementText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Element")));
	if (ConfirmText) ConfirmText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Confirm")));
	if (RaceTitleText) RaceTitleText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_RaceTitle")));
	if (RaceHumanText) RaceHumanText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Human")));
	if (RaceElfText) RaceElfText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Elf")));
	if (RaceDwarfText) RaceDwarfText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Dwarf")));
	if (GenderTitleText) GenderTitleText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Gender")));
	if (GenderMaleText) GenderMaleText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Male")));
	if (GenderFemaleText) GenderFemaleText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Female")));
	if (ClassTitleText) ClassTitleText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Class")));
	if (ClassWarriorText) ClassWarriorText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Warrior")));
	if (ClassArcherText) ClassArcherText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Archer")));
	if (ClassMageText) ClassMageText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Wizard")));
	if (NameTitleText) NameTitleText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Name")));

	SetAllSubMenuToDefault();
	SetMainSelection(0);
}

void UCreateCharacter::NativeConstruct()
{
	Super::NativeConstruct();

	AAwPlayerController* AwPC = UGameplayFuncLib::GetPlayerControllerByWidget(this);
	if (AwPC && AwPC->GameControlState != EGameControlState::Title)
		AwPC->GameControlState = EGameControlState::Title;
}

void UCreateCharacter::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
	if(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("Title_Up", EAwInputState::Press, true))
	{
		UGameplayFuncLib::PlayUIAudio("ConfirmKey_Next");
		
		switch (SubState)
		{
		case ECreateChaSubState::MainMenu:
			PrevMain();
			break;
		
		case ECreateChaSubState::EditName:
		case ECreateChaSubState::RaceSelect:
		case ECreateChaSubState::GenderSelect:
		case ECreateChaSubState::ClassSelect:
			break;
			
		default:break;
		}
	}
	else if(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("Title_Down", EAwInputState::Press, true))
	{
		UGameplayFuncLib::PlayUIAudio("ConfirmKey_Next");
		switch (SubState)
		{
		case ECreateChaSubState::MainMenu:
			NextMain();
			break;
		
		case ECreateChaSubState::EditName:
		case ECreateChaSubState::RaceSelect:
		case ECreateChaSubState::GenderSelect:
		case ECreateChaSubState::ClassSelect:
			break;
			
		default:break;
		}
		
	}

	if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("Title_Cross", EAwInputState::Press, true))
	{
		UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
		switch (SubState)
		{
		case ECreateChaSubState::MainMenu:
			MainSelectionSelected(MainMenuIndex);
			break;
		
		case ECreateChaSubState::EditName:
		case ECreateChaSubState::RaceSelect:
		case ECreateChaSubState::GenderSelect:
		case ECreateChaSubState::ClassSelect:
			SetMainSelection(MainMenuIndex);
			break;
			
			default:break;
		}
	}
	else if (UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->IsActionOccur("Title_Eclipse", EAwInputState::Press, true))
	{
		UGameplayFuncLib::PlayUIAudio("ConfirmKey_No");
		switch (SubState)
		{
		case ECreateChaSubState::MainMenu:
			//MainSelectionSelected(MainMenuIndex);
			BackToMainMenu();
			break;
		
		case ECreateChaSubState::EditName:
		case ECreateChaSubState::RaceSelect:
		case ECreateChaSubState::GenderSelect:
		case ECreateChaSubState::ClassSelect:
			SetMainSelection(MainMenuIndex);
			break;
			
		default:break;
		}
	}
}


void UCreateCharacter::SetMainSelection(int Index)
{
	MainMenuIndex = Index;
	
	TArray<float> Pos;
		for (int i = 0; i < 7; i++)
			Pos.Add(-250 + i * 72);
		Pos.Add(360);
	TArray<FText> ShowText;
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Name")));
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Race")));
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Gender")));
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Class")));
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Voice")));
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Appearance")));
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Element")));
		ShowText.Add(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Confirm")));
	if (SelectMenuPanel)
	{
		UCanvasPanelSlot* CPS = Cast<UCanvasPanelSlot>(SelectMenuPanel->Slot);
		if (CPS) CPS->SetPosition(FVector2D(52.0f, Pos[Index]));
	}
	if (SelectedMenuText)
	{
		SelectedMenuText->SetText(ShowText[Index]);
	}

	HideAllSubPanelUnderState();
	SubState = ECreateChaSubState::MainMenu;
}

void UCreateCharacter::ShowSubPanelUnderState(int Index)
{
	// if (MainSelectionDialog) MainSelectionDialog->SetRenderOpacity(0.3f);
	if (InputNameDialog) InputNameDialog->SetVisibility(Index == 0 ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	if (RaceSelectionDialog) RaceSelectionDialog->SetVisibility(Index == 1 ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	if (SelectGenderDialog) SelectGenderDialog->SetVisibility(Index == 2 ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	if (SelectClassDialog) SelectClassDialog->SetVisibility(Index == 3 ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
	
}

void UCreateCharacter::HideAllSubPanelUnderState()
{
	// if (MainSelectionDialog) MainSelectionDialog->SetRenderOpacity(1.f);
	if (InputNameDialog) InputNameDialog->SetVisibility( ESlateVisibility::Hidden);
	if (RaceSelectionDialog) RaceSelectionDialog->SetVisibility(ESlateVisibility::Hidden);
	if (SelectGenderDialog) SelectGenderDialog->SetVisibility( ESlateVisibility::Hidden);
	if (SelectClassDialog) SelectClassDialog->SetVisibility( ESlateVisibility::Hidden);
}

void UCreateCharacter::SetAllSubMenuToDefault()
{
	if (NameEditableText) NameEditableText->SetText(FText::FromString(FString("Awakener")));
	if (SelectedRaceText) SelectedRaceText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Human")));
	if (SelectedGenderText) SelectedGenderText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Male")));
	if (ClassSelectText) ClassSelectText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("CreateCha_Warrior")));
}

void UCreateCharacter::NextMain()
{
	MainMenuIndex += 1;
	if (MainMenuIndex > 3 && MainMenuIndex < 7)
	{
		MainMenuIndex = 7;	//特殊处理跳过不能选的
	}else if (MainMenuIndex >= 7)
	{
		MainMenuIndex = 7;
	}
	SetMainSelection(MainMenuIndex);
}

void UCreateCharacter::PrevMain()
{
	MainMenuIndex -= 1;
	if (MainMenuIndex > 3 && MainMenuIndex < 7)
	{
		MainMenuIndex = 3;	//特殊处理跳过不能选的
	}else if (MainMenuIndex <= 0)
	{
		MainMenuIndex = 0;
	}
	SetMainSelection(MainMenuIndex);
}

void UCreateCharacter::MainSelectionSelected(int Index)
{
	ShowSubPanelUnderState(Index);
	switch ( Index)
	{
	case 0: SubState = ECreateChaSubState::EditName; break;
	case 1: SubState = ECreateChaSubState::RaceSelect; break;
	case 2: SubState = ECreateChaSubState::GenderSelect; break;
	case 3: SubState = ECreateChaSubState::ClassSelect; break;
	case 7:
		{
			TArray<FString> OkEvents;
			const FString GuyName = NameEditableText ? NameEditableText->GetText().ToString(): "Awakener";
			OkEvents.Add(
				FString("MessageDlgFunc.SaveChaAndStartGame(")
				.Append(GuyName )
				.Append(")")
			);
			TArray<FString> NoEvents;
			NoEvents.Add("MessageDlgFunc.CreateChaReturnLastStep()");
			UKismetSystemLibrary::PrintString(this, FString("Creating Cha : ").Append(GuyName));
			
			UGameplayFuncLib::GetAwGameInstance()->UIManager->ShowMessageDialog("CreateCha_ConfirmDialog", OkEvents, NoEvents);
		}
		break;
		default:break;
	}
}

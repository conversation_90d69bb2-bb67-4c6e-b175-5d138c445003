// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Base/BaseUI.h"
#include "UObject/Object.h"
#include "GetPlotProps.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UGetPlotProps : public UBaseUI
{
	GENERATED_BODY()

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* PlotPropsImage;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* PlotPropsTextBlock;




	UFUNCTION(BlueprintCallable)
	void SetPlotProps(FString PlotPropsId) const;
};

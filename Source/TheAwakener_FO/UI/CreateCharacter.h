// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/CanvasPanel.h"
#include "Components/EditableText.h"
#include "Components/TextBlock.h"
#include "CreateCharacter.generated.h"

UENUM()
enum class ECreateChaSubState : uint8
{
	MainMenu,		//左侧选择
	EditName,			//输入名字
	RaceSelect,		//选种族
	GenderSelect,	//选性别
	ClassSelect,		//选职业
	Confirming,		//最终确定中
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCreateCharacter : public UUserWidget
{
	GENERATED_BODY()
private:
	UPROPERTY()
	UTextBlock* TitleText;
	UPROPERTY()
	UTextBlock* NameText;
	UPROPERTY()
	UTextBlock* RaceText;
	UPROPERTY()
	UTextBlock* GenderText;
	UPROPERTY()
	UTextBlock* ClassText;
	UPROPERTY()
	UTextBlock* VoiceText;
	UPROPERTY()
	UTextBlock* AppearanceText;
	UPROPERTY()
	UTextBlock* ElementText;
	UPROPERTY()
	UTextBlock* ConfirmText;
	UPROPERTY()
	UTextBlock* SelectedMenuText;
	UPROPERTY()
	UTextBlock* SelectedRaceText;
	UPROPERTY()
	UTextBlock* RaceTitleText;
	UPROPERTY()
	UTextBlock* RaceHumanText;
	UPROPERTY()
	UTextBlock* RaceElfText;
	UPROPERTY()
	UTextBlock* RaceDwarfText;
	UPROPERTY()
	UTextBlock* GenderTitleText;
	UPROPERTY()
	UTextBlock* GenderMaleText;
	UPROPERTY()
	UTextBlock* GenderFemaleText;
	UPROPERTY()
	UTextBlock* SelectedGenderText;
	UPROPERTY()
	UTextBlock* ClassTitleText;
	UPROPERTY()
	UTextBlock* ClassWarriorText;
	UPROPERTY()
	UTextBlock* ClassArcherText;
	UPROPERTY()
	UTextBlock* ClassMageText;
	UPROPERTY()
	UTextBlock* ClassSelectText;
	UPROPERTY()
	UTextBlock* NameTitleText;
	UPROPERTY()
	UEditableText* NameEditableText;

	UPROPERTY()
	UCanvasPanel* MainSelectionDialog;
	UPROPERTY()
	UCanvasPanel* RaceSelectionDialog;
	UPROPERTY()
	UCanvasPanel* SelectMenuPanel;
	UPROPERTY()
	UCanvasPanel* SelectRacePanel;
	UPROPERTY()
	UCanvasPanel* SelectGenderDialog;
	UPROPERTY()
	UCanvasPanel* SelectGenderPanel;
	UPROPERTY()
	UCanvasPanel* SelectClassDialog;
	UPROPERTY()
	UCanvasPanel* SelectClassPanel;
	UPROPERTY()
	UCanvasPanel* InputNameDialog;
	
	//当前的子状态
	UPROPERTY()
	ECreateChaSubState SubState = ECreateChaSubState::MainMenu;
	//主菜单选中的
	UPROPERTY()
	int MainMenuIndex = 0;

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

	UFUNCTION()
	void ShowSubPanelUnderState(int Index);
	UFUNCTION()
	void HideAllSubPanelUnderState();
	//把所有面板设为默认值，因为现在那些面板不能操作，所以就是写死的凑效果
	UFUNCTION()
	void SetAllSubMenuToDefault();
	UFUNCTION()
	void MainSelectionSelected(int Index);

	

public:
	UFUNCTION()
	void SetMainSelection(int Index);
	//切换下一个或者上一个选项
	UFUNCTION()
	void NextMain();
	UFUNCTION()
	void PrevMain();

	UFUNCTION()
	int GetMainMenuIndex() { return MainMenuIndex;}

	UFUNCTION(BlueprintImplementableEvent)
	void BackToMainMenu();
};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/IUserObjectListEntry.h"
#include "Blueprint/UserWidget.h"
#include "Components/Button.h"
#include "Components/Image.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingUIInfo.h"
#include "ArmorSlot.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UArmorSlot : public UUserWidget, public IUserObjectListEntry
{
	GENERATED_BODY()
private:
	virtual FReply NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent) override;
	virtual void NativeOnFocusLost(const FFocusEvent& InFocusEvent) override;

	UPROPERTY()
	UImage* ArmorIconImage;
	UPROPERTY()
	UImage* SelectedMaskImage;
	

	FThingUIInfo ArmorUIInfo;
	
	virtual void NativeOnInitialized() override;
	virtual void NativeOnListItemObjectSet(UObject* ListItemObject) override;
	virtual void NativeOnItemSelectionChanged(bool bIsSelected) override;
public:
	UPROPERTY()
	UButton* ThisButton;
	//Armor其实就是Equipment了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FEquipment Equipment;

	//在背包的index，<0就不在背包了呗
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int IndexInRole = -1;

	UFUNCTION()
	void TryWearThisEquipment();

	void Draw();
	void SetAsUsingEquipment(FEquipment ThisEquipment);
	void SetAsEquipmentInBackpack(int Index);
	void SetAsNothing();
};


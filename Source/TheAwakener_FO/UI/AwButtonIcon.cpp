// Fill out your copyright notice in the Description page of Project Settings.


#include "AwButtonIcon.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UButtonIcon::OnOpen()
{
	if (GetGameInstance())
		UAwGameInstance::Instance->InputTypeChangeDelegate.AddDynamic(this, &UButtonIcon::RefreshButtonImage);
}

void UButtonIcon::OnClose()
{
	if (GetGameInstance())
		UAwGameInstance::Instance->InputTypeChangeDelegate.RemoveDynamic(this, &UButtonIcon::RefreshButtonImage);
}

void UButtonIcon::RefreshButtonImage()
{
	SetButtonImage(ButtonIcon);
}

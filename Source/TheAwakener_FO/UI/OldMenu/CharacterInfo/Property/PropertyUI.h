// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BasePanel.h"
#include "UMG.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "PropertyPassiveUI.h"
#include "PropertyUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPropertyUI : public UBasePanel
{
	GENERATED_BODY()
	
public:

	UPROPERTY(BlueprintReadWrite, Category = "PropertyUI|Property")
	TArray<UPropertyPassiveUI*> PassiveSkillArray;

public:

	UFUNCTION(BlueprintNativeEvent, Category = "PropertyUI|BaseFuc")
	void Initial(int Points);
	virtual void Initial_Implementation(int Points);

	UFUNCTION(BlueprintCallable)
	UPropertyPassiveUI* CreateSkillPassiveUI(UCanvasPanel* PassivePanel, int Index);
};

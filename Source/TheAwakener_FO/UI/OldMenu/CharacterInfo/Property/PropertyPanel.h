// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BasePanel.h"
#include "PropertyUI.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "MediaSource.h"
#include "PropertyPanel.generated.h"

USTRUCT(BlueprintType)
struct  FMediaInfo
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadWrite)
	FString Name;

	UPROPERTY(BlueprintReadWrite)
	UMediaSource* Source = nullptr;

	UPROPERTY(BlueprintReadWrite)
	FString Desc;

};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPropertyPanel : public UBasePanel
{
	GENERATED_BODY()
	
public:

	//创建的PropertyUI数组，该Property显示Bar和自身底下的PassiveUI
	UPROPERTY(BlueprintReadWrite, Category = "SubMenu|Character|Property")
	TArray<UPropertyUI*> PropertyArray;

	


public:

	//初始化
	UFUNCTION(BlueprintCallable, Category = "SubMenu|Fuc")
	void PropertyInitial();

	UFUNCTION(BlueprintCallable)
	void CreatePropertyUI(UCanvasPanel* PropertyUIPanel);

	//确认之后修改character上的BaseTalent的函数
	UFUNCTION(BlueprintCallable)
	bool AssureSetCharacterBaseTalent(bool Validation = true);

	//CanOffserPoint
	UFUNCTION(BlueprintCallable)
	int CanOfferPoint(int TalentIndex, int Points = 1);

	//SetUIBaseTalent
	UFUNCTION(BlueprintCallable)
	void SetPointToUIBaseTalent(int TalentIndex, int Points);

	//刷新Character的信息
	UFUNCTION(BlueprintCallable)
	void ResetToClasssBase(FBattleClassModel ClassInfo);

	//洗点
	UFUNCTION(BlueprintCallable)
	void ClearTalent(int TalentIndex);

	//GetPropertyInfo
	UFUNCTION(BlueprintPure)
	int CanUsePoints(int Level);

	//获取Character的等级
	UFUNCTION(BlueprintPure)
	int GetLevel(AAwCharacter* Character);

	//获取Character的经验
	UFUNCTION(BlueprintPure)
	int GetExp(AAwCharacter* Character);

	//返回已加好点的数据
	UFUNCTION(BlueprintPure)
	int GetTalentPoints(int TalentIndex);

	//返回加点后的数据（包括预览的点数）
	UFUNCTION(BlueprintPure)
	int GetSelfTalentPoints(int TalentIndex);

	//获得Character的面板属性
	UFUNCTION(BlueprintPure)
	void GetCharacterProp(int& Attack, int& Defend, int& HP, int& MP, int& SP);

};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "AwButtonIcon.generated.h"

UENUM(BlueprintType)
enum class EAwButtonIcon: uint8
{
	// Ps5 - Circle
	// XBoxS - B
	// KeyBoard - Alt
	Circle,
	// Ps5 - Cross
	// XBoxS - A
	// KeyBoard - Space
	Cross,
	// Ps5 - Square
	// XBoxS - X
	// KeyBoard - LeftMouse
	Square,
	// Ps5 - Triangle
	// XBoxS - Y
	// KeyBoard - Q
	Triangle,
	// Ps5 - L1
	// XBoxS - LB
	// KeyBoard - F
	L1,
	// Ps5 - R1
	// XBoxS - RB
	// KeyBoard - E
	R1,
	// Ps5 - L2
	// XBoxS - LT
	// KeyBoard - RightMouse
	L2,
	// Ps5 - R2
	// XBoxS - RT
	// KeyBoard - R
	R2,
	// Ps5 - L3
	// XBoxS - L3
	// KeyBoard - Shift
	L3,
	// Ps5 - R3
	// XBoxS - R3
	// KeyBoard - MidMouse
	R3,
	// 左边的菜单
	// Ps5 - Options
	// XBoxS - View
	// KeyBoard - Esc
	Options,
	// 右边的菜单
	// Ps5 - Share
	// XBoxS - Menu
	// KeyBoard - Esc
	Share,
	// Ps5 - ↑
	// XBoxS - ↑
	// KeyBoard - C
	Dpad_Up,
	// ↓
	// Ps5 - ↑
	// XBoxS - ↑
	// KeyBoard - C
	DPad_Down,
	// Ps5 - ←
	// XBoxS - ←
	// KeyBoard - Tab
	DPad_Left,
	// Ps5 - →
	// XBoxS - →
	// KeyBoard - Tab
	DPad_Right,
	// Ps5 - ↑ ↓
	// XBoxS - ↑ ↓
	// KeyBoard - W & S
	DPad_Up_And_Down,
	// Ps5 - ← →
	// XBoxS - ← →
	// KeyBoard - A & D
	DPad_Left_And_Right,

	None
};

USTRUCT(BlueprintType)
struct FInputButtonSetting
{
	GENERATED_BODY()

public:
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString StrKey = "";
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EAwButtonIcon ButtonIcon = EAwButtonIcon::None;
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UButtonIcon : public UUserWidget
{
	GENERATED_BODY()
	
public:
	// 放蓝图调用，不然一直有编辑器报错
	UFUNCTION(BlueprintCallable)
	void OnOpen();
	// 放蓝图调用，不然一直有编辑器报错
	UFUNCTION(BlueprintCallable)
	void OnClose();
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EAwButtonIcon ButtonIcon = EAwButtonIcon::Circle;
	
	UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
	void SetButtonImage(EAwButtonIcon NewButtonIcon);

	UFUNCTION(BlueprintCallable)
	void RefreshButtonImage();
};

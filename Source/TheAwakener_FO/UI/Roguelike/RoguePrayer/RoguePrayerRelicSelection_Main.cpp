// Fill out your copyright notice in the Description page of Project Settings.


#include "RoguePrayerRelicSelection_Main.h"

#include "TheAwakener_FO/GameFramework/Base/AwGameState.h"

void URoguePrayerRelicSelection_Main::NativeOnInitialized()
{
	Super::NativeOnInitialized();
}

void URoguePrayerRelicSelection_Main::NativeConstruct()
{
	Super::NativeConstruct();
	UpdateShowMouse();
}

void URoguePrayerRelicSelection_Main::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
	if (GetAwGameState())
	{
		AAwCharacter* Me = GetAwGameState()->GetMyCharacter();

	}
}

void URoguePrayerRelicSelection_Main::NativeDestruct()
{
	Super::NativeDestruct();
}

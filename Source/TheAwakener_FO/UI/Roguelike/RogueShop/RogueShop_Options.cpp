// Fill out your copyright notice in the Description page of Project Settings.


#include "RogueShop_Options.h"

void URogueShop_Options::NativeOnInitialized()
{
	Super::NativeOnInitialized();
}

void URogueShop_Options::NativeConstruct()
{
	Super::NativeConstruct();
}

void URogueShop_Options::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void URogueShop_Options::NativeDestruct()
{
	Super::NativeDestruct();
}

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"

#include "RogueShop_Options.generated.h"

/**
 * 
 */
//商品类型
UENUM(BlueprintType)
enum class ECommoditiesType : uint8
{
	//圣遗物
	Relic,
	//主动道具
	ActiveProps,
	//其他
	Other
};

UCLASS()
class THEAWAKENER_FO_API URogueShop_Options : public UBaseUI
{
	GENERATED_BODY()

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	virtual void NativeDestruct() override;

public:
	
};

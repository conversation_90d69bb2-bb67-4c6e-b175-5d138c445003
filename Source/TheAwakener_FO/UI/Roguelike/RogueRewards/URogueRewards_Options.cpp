// Fill out your copyright notice in the Description page of Project Settings.


#include "URogueRewards_Options.h"

void UURogueRewards_Options::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	RewardsName_TextBlock = Cast<UTextBlock>(GetWidgetFromName(TEXT("RewardsName")));

	RewardsDesc_TextBlock = Cast<UTextBlock>(GetWidgetFromName(TEXT("RewardsDesc")));

	RewardsDesc_RichTextBlock = Cast<URichTextBlock>(GetWidgetFromName(TEXT("RewardsDesc_RichText")));
}

void UURogueRewards_Options::NativeConstruct()
{
	Super::NativeConstruct();
}

void UURogueRewards_Options::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UURogueRewards_Options::NativeDestruct()
{
	Super::NativeDestruct();
}

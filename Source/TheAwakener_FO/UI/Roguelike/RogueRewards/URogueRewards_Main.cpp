// Fill out your copyright notice in the Description page of Project Settings.


#include "URogueRewards_Main.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void URogueRewards_Main::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	Rewards_HorizontalBox = Cast<UHorizontalBox>(GetWidgetFromName(TEXT("RewardsHorizontalBox")));
}

void URogueRewards_Main::NativeConstruct()
{
	Super::NativeConstruct();

	//UGameplayFuncLib::GetMyAwPlayerController()->GameControlState = EGameControlState::RogueRewards;

	GameState = UGameplayFuncLib::GetAwGameState();

	UpdateShowMouse();
}

void URogueRewards_Main::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	UpdateShowMouse();
	if (GameState)
	{
		AAwCharacter* Me = GameState->GetMyCharacter();

		if(Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Left",EAwInputState::Press,true) ||
			Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Left",EAwInputState::Hold,true))
		{
			FUNC_SelectedLeft();
		}
		else if(Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Right",EAwInputState::Press,true) ||
				Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Right",EAwInputState::Hold,true))
		{
			FUNC_SelectedRight();
		}
		/*else if(Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Confirm",EAwInputState::Press,true))
		{
  			FUNC_SelectedConfirm();
		}
		else if(Me->GetCmdComponent()->IsActionOccur("RogueRewardsAndRoom_Refuse",EAwInputState::Press,true))
		{
			FUNC_Back();
		}*/
	}
}

void URogueRewards_Main::NativeDestruct()
{
	Super::NativeDestruct();
}

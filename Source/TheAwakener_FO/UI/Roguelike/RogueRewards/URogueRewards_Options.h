// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/RichTextBlock.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"

#include "URogueRewards_Options.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UURogueRewards_Options : public UBaseUI
{
	GENERATED_BODY()


	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	virtual void NativeDestruct() override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* RewardsName_TextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* RewardsDesc_TextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	URichTextBlock* RewardsDesc_RichTextBlock;
	
};

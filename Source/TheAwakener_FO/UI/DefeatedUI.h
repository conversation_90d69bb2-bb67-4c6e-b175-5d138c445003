// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/Button.h"
#include "GameMain/ThreeForces.h"
#include "UObject/Object.h"
#include "DefeatedUI.generated.h"

/**
 * 战败界面，暂时凑个效果
 */
UCLASS()
class THEAWAKENER_FO_API UDefeatedUI : public UUserWidget
{
	GENERATED_BODY()
private:

	UPROPERTY()
	UTextBlock* MainTitleText;
	UPROPERTY()
	UTextBlock* ListTitleText;
	UPROPERTY()
	UTextBlock* BackButtonText;

	UPROPERTY()
	UAwGameInstance* GameInstance;
	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	
	//按钮
	UPROPERTY()
	UButton* RestartButton;

	UFUNCTION()
	void Restart();

public:

	UPROPERTY()
	UThreeForces* ThreeForces;
	
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void GoBackToVillage();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void GoToRespawnPoint();

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString UIKey = "Defeated";
};

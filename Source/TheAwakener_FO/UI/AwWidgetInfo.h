#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "AwWidgetInfo.generated.h"

USTRUCT(BlueprintType)
struct FAwWidgetInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id = "";
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Path;
	//ui的ZOrder值
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ZOrder = 0;

	static FAwWidgetInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAwWidgetInfo Model;
		Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
		Model.Path = UDataFuncLib::AwGetStringField(JsonObj, "Path");
		Model.ZOrder = JsonObj->GetNumberField("ZOrder");

		return Model;
	}
};

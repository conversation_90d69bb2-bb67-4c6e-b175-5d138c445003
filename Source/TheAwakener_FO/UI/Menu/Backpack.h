// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ItemHint.h"
#include "Blueprint/UserWidget.h"
#include "Components/TileView.h"
#include "Backpack.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBackpack : public UUserWidget
{
	GENERATED_BODY()
	virtual void NativeOnInitialized() override;
public:
	UPROPERTY()
	UTileView* ItemListComp;
	UPROPERTY()
	UItemHint* ItemHintComp;

	//当背包内物品发生变化的时候（无论是武器还是防具还是道具）
	void RefreshItems();

	UFUNCTION()
	void ShowItemObjHint(FItemObj ItemObj);
	UFUNCTION()
	void ShowWeaponObjHint(FEquippedWeaponSet WeaponObj);
	UFUNCTION()
	void ShowEquipmentHint(FEquipment Equipment);
};

// Fill out your copyright notice in the Description page of Project Settings.


#include "Party.h"

#include "ElementalTalent/ElementalIcon.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UParty::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	this->TalentListView = Cast<UListView>(GetWidgetFromName(TEXT("TalentList")));
	this->Hint = Cast<UElementalTalentHint>(GetWidgetFromName(TEXT("WBP_ElementalHint")));

	ToShowInfo = UGameplayFuncLib::GetAwDataManager()->AllElementalTalentInfo();
	
	if (TalentListView && UAwGameInstance::Instance)
	{
		for (const FElementalTalentUIInfo SInfo : ToShowInfo)
		{
			UElementalIcon* EleIcon = UAwGameInstance::Instance->UIManager->LoadUserWidget<UElementalIcon>("Core/UI/Menu/WBP_ElementalTalent");
			if (EleIcon)
			{
				const bool HasLearnt = SInfo.Level <= 3;
				EleIcon->Set(this->Hint, SInfo, HasLearnt);
				TalentListView->AddItem(EleIcon);
			}
		} 
	}
	if (Hint)
	{
		Hint->SetVisibility(ESlateVisibility::Hidden);
	}
}

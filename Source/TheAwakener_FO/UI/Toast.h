// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/CanvasPanel.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "UObject/Object.h"
#include "Toast.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UToast : public UUserWidget
{
	GENERATED_BODY()
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	UPROPERTY()
	float StayTime = 0;
	UPROPERTY()
	float TargetY = 300;
	UPROPERTY()
	bool Arrived = false;

	UPROPERTY()
	float IsShow = false;
public:

	UPROPERTY(VisibleAnywhere,BlueprintReadWrite)
	FString Key;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float ExistenceTimeMin = 1.5f;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float ExistenceTimeMax = 2.5f;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float HideSpeed = 1.0f;
	
	UPROPERTY()
	UCanvasPanelSlot* PanelSlot;
	UPROPERTY()
	UTextBlock* TextBlock;

	UPROPERTY()
	UImage* ToastBkgImage;

	UPROPERTY()
	UCanvasPanel* ToastPanel;

	UFUNCTION(BlueprintCallable)
	void Show(FString Text, float Y = 400);

	UFUNCTION(BlueprintCallable)
	void Hide();
};

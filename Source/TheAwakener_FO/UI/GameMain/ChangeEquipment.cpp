// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeEquipment.h"

#include <use_ansi.h>


#include "EngineUtils.h"
#include "GameMenu.h"
#include "ModelMappingToUI.h"
#include "TimerManager.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/HorizontalBoxSlot.h"
#include "Components/OverlaySlot.h"
#include "Engine/Canvas.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void UChangeEquipment::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	GameState = UGameplayFuncLib::GetAwGameState();

	Equipped_BackgroundBlur				= Cast<UBackgroundBlur>(GetWidgetFromName("EquippedBackgroundBlur"));
	EquipmentMenuList					= Cast<UMenuList>(GetWidgetFromName("WBP_MenuList"));
	ItemList 							= Cast<UMenuList>(GetWidgetFromName("WBP_ItemList"));
	ItemModelImage						= Cast<UImage>(GetWidgetFromName("ItemModel"));
	ItemImage							= Cast<UImage>(GetWidgetFromName("Item"));
	Equipped 							= Cast<UEquipmentInformation>(GetWidgetFromName("WBP_Equipped"));
	NotEquipped							= Cast<UEquipmentInformation>(GetWidgetFromName("WBP_NotEquipped"));
	ArrowImage							= Cast<UImage>(GetWidgetFromName("Arrow"));
	PlayerAttributBarBase				= Cast<UCanvasPanel>(GetWidgetFromName("PlayerAttributBar"));
	AttributUI_HP						= Cast<UPlayerAttributUI>(GetWidgetFromName("WBP_HP"));
	AttributUI_ATK 						= Cast<UPlayerAttributUI>(GetWidgetFromName("WBP_ATK"));
	AttributUI_DEF 						= Cast<UPlayerAttributUI>(GetWidgetFromName("WBP_DEF"));
	PlayerNameTextBlock					= Cast<UTextBlock>(GetWidgetFromName("PlayerName"));
	EXP_Bar								= Cast<UProgressBar>(GetWidgetFromName("EXPBar"));
	PlayerLevel							= Cast<UTextBlock>(GetWidgetFromName("Level"));
	ClassIconImage						= Cast<UImage>(GetWidgetFromName("ClassIcon"));
	EXP_VFX								= Cast<UImage>(GetWidgetFromName("EXPVFX"));
	CurExpTextBlock						= Cast<UTextBlock>(GetWidgetFromName("CurEXP"));
	NeedExpTextBlock					= Cast<UTextBlock>(GetWidgetFromName("NeedEXP"));


	ItemList->SetVisibility(ESlateVisibility::Collapsed);
	Equipped->SetVisibility(ESlateVisibility::Collapsed);
	NotEquipped->SetVisibility(ESlateVisibility::Collapsed);
	ArrowImage->SetVisibility(ESlateVisibility::Collapsed);
	ItemModelImage->SetVisibility(ESlateVisibility::Collapsed);
	ItemImage->SetVisibility(ESlateVisibility::Collapsed);

	UILevel = SelectType;
	LevelChanging = false;
	LevelChangOver = true;
	IsEquippedItem = false;

	
	SetPlayerAttirbutUI();
	
	for(TActorIterator<AModelMappingToUI>Iterator(GetWorld());Iterator;++Iterator)
	{
		ModelMappingToUI = *Iterator;
	}
	
	GetWorld()->GetTimerManager().SetTimer(TimerHandle,this,&UChangeEquipment::SetGameUIControllState,0.3f,true);
	GetWorld()->GetTimerManager().SetTimer(TimerHandle1,this,&UChangeEquipment::InitEquippmentTypeList,0.2f);
}

void UChangeEquipment::NativeConstruct()
{
	Super::NativeConstruct();

	this->SetRenderOpacity(0);
	NotEquipped->EquippedOverlay->SetVisibility(ESlateVisibility::Collapsed);
	InitPlayerInfoPanel();
}

void UChangeEquipment::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	if (GameState)
	{
		const AAwCharacter* Me = GameState->GetMyCharacter();
		
		if(UGameplayFuncLib::GetWorkingAwPlayerController()->GameUIControlState == EGameUIControlState::SecondaryUIState)
		{
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Up") || Me->GetCmdComponent()->IsActionOccur("Menu_Up",EAwInputState::Hold))
			{
				if(EquipmentMenuList && EquipmentMenuList->MenuList->GetNumItems())
					EquipmentMenuList->ListSelectUp();
			}
		
			else if(Me->GetCmdComponent()->IsActionOccur("Menu_Down") || Me->GetCmdComponent()->IsActionOccur("Menu_Down",EAwInputState::Hold))
			{
				if(EquipmentMenuList && EquipmentMenuList->MenuList->GetNumItems())
					EquipmentMenuList->ListSelectDown();
			}
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Confirm",EAwInputState::Press,true))
			{
				if(!GetIsAnimPlay())
				{
					UGameplayFuncLib::PlayUIAudio("ConfirmKey_Next");
					SetIsOpenChildUI(true);
					SetIsSelfCloss(false);
					Back();
				}
				
			}
		
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Refuse",EAwInputState::Press,true))
			{
				if(LevelChanging == false)
				{
					if(UILevel == SelectType)
					{
						if(!GetIsAnimPlay())
						{
							UGameplayFuncLib::PlayUIAudio("ConfirmKey_Back");
							SetIsSelfCloss(false);
							Back();
						}
						
					}
				}
			}
		}
		else if(UGameplayFuncLib::GetWorkingAwPlayerController()->GameUIControlState == EGameUIControlState::TertiaryMenu)
		{
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Up",EAwInputState::Press) || Me->GetCmdComponent()->IsActionOccur("Menu_Up",EAwInputState::Hold))
			{
				if(ItemList && ItemList->MenuList->GetNumItems() && ItemList->GetSelectListEntry())
				{
					ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					ItemList->ListSelectUp();
					if(ItemList->GetSelectListEntry())
						ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
					if(IsValid(ItemList) && !ItemList->CurSelectIsToporDown)
						UpdateView();
				}
					
			}
		
			else if(Me->GetCmdComponent()->IsActionOccur("Menu_Down",EAwInputState::Press) || Me->GetCmdComponent()->IsActionOccur("Menu_Down",EAwInputState::Hold))
			{
				if(ItemList && ItemList->MenuList->GetNumItems() && ItemList->GetSelectListEntry())
				{
					ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					ItemList->ListSelectDown();
					if(ItemList->GetSelectListEntry())
						ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
					if(IsValid(ItemList) && !ItemList->CurSelectIsToporDown)
						UpdateView();
				}
			}
			
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Confirm",EAwInputState::Press,true))
			{
				if(ItemList && !ItemList->MenuList->GetSelectedItem<UMenuListEntryData>()->IsEquipped)
				{
					UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
					WearEquipment();
					UpdateView();
				}
			}
		
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Refuse",EAwInputState::Press,true))
			{
				if(LevelChanging == false)
				{
					if(UILevel == SelectContent)
					{
						if(!GetIsAnimPlay())
						{
							UGameplayFuncLib::PlayUIAudio("ConfirmKey_Back");
							SetIsSelfCloss(false);
							Back();
						}
					}
				}
			}
		}
		
		if(LevelChanging)
		{
			LevelChanging = false;
			GetWorld()->GetTimerManager().SetTimer(TimerHandle,this,&UChangeEquipment::SetUILevel,0.2f,true);
		}
		ChangeUILevel();
		UpdateAnimPlay();
	}
	else
	{
		GameState = UGameplayFuncLib::GetAwGameState();
	}
}

void UChangeEquipment::OpenChildUI()
{
	if(EquipmentMenuList->MenuList->GetNumItems() > 0)
	{
		ItemList->MenuList->ClearListItems();
		UGameplayFuncLib::SetAllPlayerUIControlState(EGameUIControlState::TertiaryMenu);
		EEquipmentPart EquipmentPart = UDataFuncLib::FStringToEnum<EEquipmentPart>(EquipmentMenuList->GetSelectUIName());
		FListItemElementInfo TempItem = UGameplayFuncLib::GetDataManager()->GetListItemsById("LevelTwo");
		FThingUIInfo ThingUIInfo;
		FListItems ListItem = TempItem.Items[0];
		TempItem.Items.Empty();

		if(EquipmentPart == EEquipmentPart::Weapon)
		{
			UTexture2D* TempIcon = LoadObject<UTexture2D>(nullptr,
				*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("WeaponATK").Path));
			Equipped->SetEquippedAttributesIcon(TempIcon);
			NotEquipped->SetEquippedAttributesIcon(TempIcon);
			
			ThingUIInfo = UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(EThingType::WeaponObj,EquipmentMenuList->
				MenuList->GetSelectedItem<UMenuListEntryData>()->ItemType);
			if(EquipmentMenuList->GetSelectData()->IsMainHand)
			{
				ListItem.EquipmentAttributValue	= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.MainHand.Model.AttackPower.Physical;
				ListItem.ItemId					= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.MainHand.Model.Id;
				ListItem.UniqueId				= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.MainHand.UniqueId;
				ListItem.Affix					= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.MainHand.EquipSets;
			}
			else
			{
				ListItem.EquipmentAttributValue	= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.OffHand.Model.AttackPower.Physical;
				ListItem.ItemId					= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.OffHand.Model.Id;
				ListItem.UniqueId				= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.OffHand.UniqueId;
				ListItem.Affix					= UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet.MainHand.EquipSets;
			}
			//ListItem.MakerImage					= ThingUIInfo.Icon;
			ListItem.Name						= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(ListItem.ItemId);
			ListItem.ListItemType				= UDataFuncLib::EnumToFString(ThingUIInfo.ThingType);
			ListItem.SceneCameraLocation		= UGameplayFuncLib::GetAwDataManager()->
			GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).SceneCameraLocation;
			ListItem.ModelRotator				= UGameplayFuncLib::GetAwDataManager()->
			GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelRotator;
			ListItem.ModelLocation				= UGameplayFuncLib::GetAwDataManager()->
			GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelLocation;
			ListItem.IsEquipped					= true;
			
			TempItem.Items.Add(ListItem);
			
			
			for (int i = 0;i < UGameplayFuncLib::GetAwGameInstance()->RoleInfo.WeaponObjs.Num();i++)
			{
				if(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.WeaponObjs[i].Model.WeaponType ==
					UDataFuncLib::FStringToEnum<EWeaponType>(EquipmentMenuList->MenuList->GetSelectedItem<UMenuListEntryData>()->ItemType))
				{
					ThingUIInfo = UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(
						EThingType::WeaponObj,EquipmentMenuList->MenuList->
						GetSelectedItem<UMenuListEntryData>()->ItemType);
					//ListItem.MakerImage						= ThingUIInfo.Icon;
					ListItem.ItemId							= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.WeaponObjs[i].Model.Id;
					ListItem.UniqueId						= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.WeaponObjs[i].UniqueId;
					ListItem.Index							= i;
					ListItem.Name							= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(ListItem.ItemId);
					ListItem.ListItemType					= UDataFuncLib::EnumToFString(ThingUIInfo.ThingType);
					ListItem.IsEquipped						= false;
					ListItem.SceneCameraLocation			= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).SceneCameraLocation;
					ListItem.ModelRotator					= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelRotator;
					ListItem.ModelLocation					= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelLocation;
					ListItem.EquipmentAttributValue			= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.WeaponObjs[i].Model.AttackPower.Physical;
					ListItem.Affix							= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.WeaponObjs[i].EquipSets;
					TempItem.Items.Add(ListItem);
				}
			}
			
			ItemList->SetEntry(TempItem);
			UTexture2D* AttirIcon = LoadObject<UTexture2D>(nullptr,
				*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("ATK").Path));
			
			if(AttirIcon)
			{
				Equipped->EquippmentAttributUI->AttirbutIcon->SetBrushFromTexture(AttirIcon);
				NotEquipped->EquippmentAttributUI->AttirbutIcon->SetBrushFromTexture(AttirIcon);
			}
			
		}
		else
		{
			UTexture2D* TempIcon = LoadObject<UTexture2D>(nullptr,
				*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("EquipmentDEF").Path));
			Equipped->SetEquippedAttributesIcon(TempIcon);
			NotEquipped->SetEquippedAttributesIcon(TempIcon);
			
			for (FEquipment Equipment : UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.Equipments)
			{
				ThingUIInfo = UGameplayFuncLib::GetAwDataManager()->GetBaseThingUIInfo(EThingType::Equipment,Equipment.Id);
				if(Equipment.PartType == EquipmentPart)
				{
					//ListItem.MakerImage						= ThingUIInfo.Icon;
					ListItem.ItemId							= Equipment.Id;
					ListItem.ListItemType					= UDataFuncLib::EnumToFString(ThingUIInfo.ThingType);
					ListItem.Name							= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(ListItem.ItemId);
					ListItem.UniqueId						= Equipment.UniqueId;
					ListItem.IsEquipped						= true;
					ListItem.SceneCameraLocation			= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).SceneCameraLocation;
					ListItem.ModelRotator					= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelRotator;
					ListItem.ModelLocation					= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelLocation;
					ListItem.EquipmentAttributValue			= Equipment.Property.PDefense;
					ListItem.Affix							= Equipment.EquipSets;
					TempItem.Items.Add(ListItem);
				}
			}
			
			for (int i = 0;i < UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs.Num();i++)
			{
				ThingUIInfo = UGameplayFuncLib::GetAwDataManager()->
				GetBaseThingUIInfo(EThingType::Equipment,UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs[i].Id);
				if(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs[i].PartType == EquipmentPart)
				{
					//ListItem.MakerImage						= ThingUIInfo.Icon;
					ListItem.ItemId							= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs[i].Id;
					ListItem.UniqueId						= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs[i].UniqueId;
					ListItem.Index							= i;
					ListItem.Name							= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(ListItem.ItemId);
					ListItem.ListItemType					= UDataFuncLib::EnumToFString(ThingUIInfo.ThingType);
					ListItem.IsEquipped						= false;
					ListItem.SceneCameraLocation			= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).SceneCameraLocation;
					ListItem.ModelRotator					= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelRotator;
					ListItem.ModelLocation					= UGameplayFuncLib::GetAwDataManager()->
					GetBaseThingUIInfo(ThingUIInfo.ThingType,ListItem.ItemId).ModelLocation;
					ListItem.EquipmentAttributValue			= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs[i].Property.PDefense;
					ListItem.Affix							= UGameplayFuncLib::GetAwGameInstance()->RoleInfo.EquipmentObjs[i].EquipSets;
					TempItem.Items.Add(ListItem);
				}
			}
			ItemList->SetEntry(TempItem);

			UTexture2D* AttirIcon = LoadObject<UTexture2D>(nullptr,
				*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("DEF").Path));
			
			if(AttirIcon)
			{
				Equipped->EquippmentAttributUI->AttirbutIcon->SetBrushFromTexture(AttirIcon);
				NotEquipped->EquippmentAttributUI->AttirbutIcon->SetBrushFromTexture(AttirIcon);
			}
		}
		
		UpdateView();
		UILevel = SelectContent;
		PlayWidgetAnim("ShowItemList",0.0f,1.0f,EUMGSequencePlayMode::Forward,1.0f,false);
		GetWorld()->GetTimerManager().SetTimer(TimerHandle1,this,&UChangeEquipment::InitEquippmentItemList,0.15f);
	}
}

void UChangeEquipment::Back()
{
	if(UILevel == SelectType)
	{
		PlayWidgetAnim("ShowMenuList",0.0f,1.0f,EUMGSequencePlayMode::Reverse,1.0f,false);
	}
	else
	{
		PlayWidgetAnim("ShowItemList",0.0f,1.0f,EUMGSequencePlayMode::Reverse,1.0f,false);
	}
	
	SetIsAnimPlay(true);
}

void UChangeEquipment::ChangeUILevel()
{
	switch (UILevel)
	{
	case SelectType:
		
		EquipmentMenuList->SetVisibility(ESlateVisibility::Visible);
		Equipped->SetVisibility(ESlateVisibility::Collapsed);
		NotEquipped->SetVisibility(ESlateVisibility::Collapsed);
		PlayerAttributBarBase->SetVisibility(ESlateVisibility::Visible);
		ItemList->SetVisibility(ESlateVisibility::Collapsed);
		ArrowImage->SetVisibility(ESlateVisibility::Collapsed);
		//ItemModelImage->SetVisibility(ESlateVisibility::Collapsed);
		ItemImage->SetVisibility(ESlateVisibility::Collapsed);
		
		break;
	case SelectContent:

		EquipmentMenuList->SetVisibility(ESlateVisibility::Collapsed);
		
		PlayerAttributBarBase->SetVisibility(ESlateVisibility::Collapsed);
		ItemList->SetVisibility(ESlateVisibility::Visible);
		//ItemModelImage->SetVisibility(ESlateVisibility::Visible);
		ItemImage->SetVisibility(ESlateVisibility::Visible);
		
		break;
	}
}

void UChangeEquipment::SetUILevel()
{
	if(UILevel == SelectContent)
	{
		UILevel = SelectType;
	}
		
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle);
}

void UChangeEquipment::SetPlayerAttirbutUI()
{
	if(AttributUI_HP)
		AttributUI_HP->SetAllContent(LoadObject<UTexture2D>(nullptr,
			*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("HP").Path)),
			GameState->GetMyCharacter()->CharacterObj.CurProperty.HP,UGameplayFuncLib::GetAwDataManager()->GetTextByKey("HP"));
	
	if(AttributUI_ATK)
		AttributUI_ATK->SetAllContent(LoadObject<UTexture2D>(nullptr,
			*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("ATK").Path)),
			GameState->GetMyCharacter()->CharacterObj.WeaponSet.MainHand.Model.AttackPower.Physical +
			GameState->GetMyCharacter()->CharacterObj.WeaponSet.OffHand.Model.AttackPower.Physical + 
			GameState->GetMyCharacter()->CharacterObj.CurProperty.PAttack,
			UGameplayFuncLib::GetAwDataManager()->GetTextByKey("ATK"));
	
	if(AttributUI_DEF)
		AttributUI_DEF->SetAllContent(LoadObject<UTexture2D>(nullptr,
				*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("DEF").Path)),
				GameState->GetMyCharacter()->CharacterObj.CurProperty.PDefense,UGameplayFuncLib::GetAwDataManager()->GetTextByKey("DEF"));
	
}

void UChangeEquipment::SetEquipped()
{
	/*Equipped->SetIsEquipped_Text(TEXT("已装备"));
	Equipped->Control_Icon->SetVisibility(ESlateVisibility::Collapsed);
	Equipped->IsEquipped_Text->SetOpacity(0.5f);*/
}

void UChangeEquipment::SetNotEquipped()
{
	/*NotEquipped->SetIsEquipped_Text(TEXT("装备"));
	UWidgetLayoutLibrary::SlotAsHorizontalBoxSlot(NotEquipped->IsEquipped_Text)->SetHorizontalAlignment(HAlign_Left);*/
}

void UChangeEquipment::SetGameUIControllState()
{
	
	UGameplayFuncLib::SetAllPlayerUIControlState(EGameUIControlState::SecondaryUIState);
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle);
}

void UChangeEquipment::UpdateView()
{
	EEquipmentPart EquipmentPart = UDataFuncLib::FStringToEnum<EEquipmentPart>(EquipmentMenuList->GetSelectUIName());
	if(!ItemList->MenuList->GetSelectedItem<UMenuListEntryData>())
		return;
	if(ItemList->MenuList->GetSelectedItem<UMenuListEntryData>()->IsEquipped)
	{
		if(IsEquippedItem)
		{
			EquippedIndex = ItemList->MenuList->GetIndexForItem(ItemList->MenuList->GetSelectedItem<UMenuListEntryData>());
			NotEquipped->SetVisibility(ESlateVisibility::Collapsed);
			Equipped->SetVisibility(ESlateVisibility::Visible);
		}
		else
		{
			IsEquippedItem = true;
			EquippedIndex = ItemList->MenuList->GetIndexForItem(ItemList->MenuList->GetSelectedItem<UMenuListEntryData>());
			Equipped->SetVisibility(ESlateVisibility::Visible);
		}
		
		FUIEquipmentInfo UIEquipmentInfo;
		UIEquipmentInfo.Name						= ItemList->GetSelectData()->Name;
		UIEquipmentInfo.EquipmentIcon				= ItemList->GetSelectData()->EntryMakerBoxImage;
		UIEquipmentInfo.EquipmentAttributValue		= FString::FromInt(ItemList->GetSelectData()->ItemAttributValue);
		UIEquipmentInfo.EquipmentDescibe			= UGameplayFuncLib::GetAwDataManager()->
		GetTextByKey(ItemList->GetSelectData()->ItemId + "Explain");
		
		Equipped->SetEquipment(UIEquipmentInfo);
		
		SetMosaicGroove(0, Equipped->MosaicGroove0);
		SetMosaicGroove(1, Equipped->MosaicGroove1);
		SetMosaicGroove(2, Equipped->MosaicGroove2);
	}
	else
	{
		FUIEquipmentInfo UIEquipmentInfo;
		NotEquipped->SetVisibility(ESlateVisibility::Visible);
		UIEquipmentInfo.Name						= ItemList->GetSelectData()->Name;
		UIEquipmentInfo.EquipmentIcon				= ItemList->GetSelectData()->EntryMakerBoxImage;
		UIEquipmentInfo.EquipmentAttributValue		= FString::FromInt(ItemList->GetSelectData()->ItemAttributValue);
		UIEquipmentInfo.EquipmentDescibe			= UGameplayFuncLib::GetAwDataManager()->
			GetTextByKey(ItemList->GetSelectData()->ItemId + "Explain");
		
		NotEquipped->SetEquipment(UIEquipmentInfo);
		
		SetMosaicGroove(0, NotEquipped->MosaicGroove0);
		SetMosaicGroove(1, NotEquipped->MosaicGroove1);
		SetMosaicGroove(2, NotEquipped->MosaicGroove2);
	}
	
	if(Equipped->GetVisibility() == ESlateVisibility::Visible && NotEquipped->GetVisibility() == ESlateVisibility::Visible)
	{
		ArrowImage->SetVisibility(ESlateVisibility::Visible);
		//UWidgetLayoutLibrary::SlotAsCanvasSlot(Equipped)->SetPosition(FVector2D(-80.0f,-230.0f));
		
		const int32 EquippedValue = FCString::Atoi(*Equipped->EquippmentAttributUI->AttirbutValue->GetText().ToString());
		const int32 NotEquippedValue = FCString::Atoi(*NotEquipped->EquippmentAttributUI->AttirbutValue->GetText().ToString());
		if(EquippedValue < NotEquippedValue)
		{
			NotEquipped->EquippmentAttributUI->AttirbutValue->SetColorAndOpacity(FSlateColor(FLinearColor::Green));
		}
		else if(EquippedValue > NotEquippedValue)
		{
			NotEquipped->EquippmentAttributUI->AttirbutValue->SetColorAndOpacity(FSlateColor(FLinearColor::Red));
		}
		else
		{
			NotEquipped->EquippmentAttributUI->AttirbutValue->SetColorAndOpacity(FSlateColor(FLinearColor::White));
		}
	}
	else
	{
		
		ArrowImage->SetVisibility(ESlateVisibility::Collapsed);
		/*if(NotEquipped->GetVisibility() == ESlateVisibility::Collapsed)
			UWidgetLayoutLibrary::SlotAsCanvasSlot(Equipped)->SetPosition(FVector2D(-80.0f,0.0f));*/
		
	}

	if(ModelMappingToUI)
	{
		ModelMappingToUI->SetSceneCaptureCameraLocation(ItemList->GetSelectData()->SceneCameraLocation);
		ModelMappingToUI->SetModelRotator(ItemList->GetSelectData()->ModelRotator);
		ModelMappingToUI->SetModelLocation(ItemList->GetSelectData()->ModelLocation);
	
		if(EquipmentPart == EEquipmentPart::Weapon)
		{
			ModelMappingToUI->ChangeEquipmentModel(UGameplayFuncLib::GetDataManager()->GetWeaponModelById(
				*ItemList->GetSelectData()->ItemId).AppearanceParts, true);
		}
		else
		{
			ModelMappingToUI->ChangeEquipmentModel(UGameplayFuncLib::GetDataManager()->GetEquipmentById(
				*ItemList->GetSelectData()->ItemId).AppearanceParts, false);
		}
	}
	SetItemImage();
}
	

void UChangeEquipment::WearEquipment()
{
	EEquipmentPart EquipmentPart = UDataFuncLib::FStringToEnum<EEquipmentPart>(EquipmentMenuList->GetSelectUIName());
	if(!ItemList->MenuList->GetSelectedItem<UMenuListEntryData>()->IsEquipped)
	{
		if(EquipmentPart == EEquipmentPart::Weapon)
		{
			FAwRoleInfo RoleInfo = UGameplayFuncLib::GetAwGameInstance()->RoleInfo;
			int WeaponObjNum = RoleInfo.WeaponObjs.Num(); 
			int i = 0;
			if(EquipmentMenuList->GetSelectData()->IsMainHand)
			{
				FEquippedWeaponSet Temp = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet;
				
				while (i < RoleInfo.WeaponObjs.Num())
				{
					FWeaponObj TemWeapon = RoleInfo.WeaponObjs[i];
					if(TemWeapon.UniqueId == ItemList->GetSelectData()->UniqueId)
					{
						Temp.MainHand = TemWeapon;
						UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->WearWeapon(Temp);
						EquipmentMenuList->GetSelectListEntry()->EntryName->SetText(
							FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.MainHand.Model.Id)));
					}
						

					if (RoleInfo.WeaponObjs.Num() < WeaponObjNum)
						WeaponObjNum = RoleInfo.WeaponObjs.Num();
					else
						i++;
				}
				
			}
			else
			{
				FEquippedWeaponSet Temp = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->CharacterObj.WeaponSet;
				
				while (i < RoleInfo.WeaponObjs.Num())
				{
					FWeaponObj TemWeapon = RoleInfo.WeaponObjs[i];
					if(TemWeapon.UniqueId == ItemList->GetSelectData()->UniqueId)
					{
						Temp.OffHand = TemWeapon;
						UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->WearWeapon(Temp);
						EquipmentMenuList->GetSelectListEntry()->EntryName->SetText(
							FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.OffHand.Model.Id)));
					}
						

					if (RoleInfo.WeaponObjs.Num() < WeaponObjNum)
						WeaponObjNum = RoleInfo.WeaponObjs.Num();
					else
						i++;
				}
			}
			
		}
		else
		{
			FAwRoleInfo RoleInfo = UGameplayFuncLib::GetAwGameInstance()->RoleInfo;
			int EquipmentObjNum = RoleInfo.EquipmentObjs.Num(); 
			int i = 0;
			while (i < RoleInfo.EquipmentObjs.Num())
			{
				FEquipment TempEquipment = RoleInfo.EquipmentObjs[i];
				if(TempEquipment.UniqueId == ItemList->GetSelectData()->UniqueId)
				{
					UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->WearEquipment(TempEquipment);
					EquipmentMenuList->GetSelectListEntry()->EntryName->SetText(
							FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(TempEquipment.Id)));
				}

				if (RoleInfo.EquipmentObjs.Num() < EquipmentObjNum)
					EquipmentObjNum = RoleInfo.EquipmentObjs.Num();
				else
					i++;
			}
			
		}
		
		Cast<UMenuListEntryData>(ItemList->MenuList->GetItemAt(EquippedIndex))->IsEquipped = false;
		UMenuListEntry* BeforeEquipped = ItemList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetItemAt(EquippedIndex));
		if(BeforeEquipped)
		{
			ItemList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetItemAt(EquippedIndex))->
			EntryTypeName->SetVisibility(ESlateVisibility::Collapsed);
			ItemList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetItemAt(EquippedIndex))->UpdateView();
		}
				
		ItemList->MenuList->GetSelectedItem<UMenuListEntryData>()->IsEquipped = true;
		UMenuListEntry* CurEquipped = ItemList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetSelectedItem<UMenuListEntryData>());
		if(CurEquipped)
		{
			ItemList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetSelectedItem<UMenuListEntryData>())->
			EntryTypeName->SetVisibility(ESlateVisibility::Visible);
			ItemList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetSelectedItem<UMenuListEntryData>())->UpdateView();
			CurEquipped->PlayWidgetAnim("Equipped",0.0f,1,EUMGSequencePlayMode::Forward,1.0f,false);
		}
	}
	SetPlayerAttirbutUI();
}

void UChangeEquipment::UpdateAnimPlay()
{
	if(GetIsAnimPlay())
	{
		if(GetIsSelfCloss())
		{
			UWidgetAnimation* TempAnim = nullptr;
			if(UILevel == SelectType)
			{
				TempAnim = GetNameWidgetAnimation("ShowMenuList");
			}
			else
			{
				TempAnim = GetNameWidgetAnimation("ShowItemList");
			}
			
			if(TempAnim)
			{
				if(!IsAnimationPlaying(TempAnim))
				{
					SetIsAnimPlay(false);
					if(ModelMappingToUI)
						DestroyEquipment();
					UGameplayFuncLib::SetAllPlayerGameControlState(EGameControlState::Game);
					//UGameplayFuncLib::GetMyAwPlayerController()->GameUIControlState = EGameUIControlState::MainUIState;
					UGameplayFuncLib::GetUiManager()->Hide("Equipment");
				}
			}
			
		}
		else
		{
			UWidgetAnimation* TempAnim = nullptr;
			if(UILevel == SelectType)
			{
				TempAnim = GetNameWidgetAnimation("ShowMenuList");
			}
			else
			{
				TempAnim = GetNameWidgetAnimation("ShowItemList");
			}

			if(TempAnim)
			{
				if(!IsAnimationPlaying(TempAnim))
				{
					SetIsAnimPlay(false);
					if(UILevel == SelectType)
					{
						if(GetIsOpenChildUI())
						{
							OpenChildUI();
							SetIsOpenChildUI(false);
						}
						else
						{
							if(ModelMappingToUI)
								DestroyEquipment();
							UGameMenu* GameMenu = Cast<UGameMenu>(UGameplayFuncLib::GetUiManager()->Show("Menu",9999));
							GameMenu->GetMenuList()->SetEntry(UGameplayFuncLib::GetDataManager()->GetListItemsById("Menu"));
							FListItemElementInfo TemItemElementInfo = UGameplayFuncLib::GetAwDataManager()->GetListItemsById("Menu");
							for (int i = 0;i < TemItemElementInfo.Items.Num();++i)
							{
								TemItemElementInfo.Items[i].Name = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(TemItemElementInfo.Items[i].Id);
							}
							GameMenu->GetMenuList()->SetEntry(TemItemElementInfo);
							GameMenu->SetListInitScroll(GetMainIndex());
							UGameplayFuncLib::SetAllPlayerGameControlState(EGameControlState::PauseMenu);
							//UGameplayFuncLib::GetMyAwPlayerController()->GameUIControlState = EGameUIControlState::MainUIState;
							UGameplayFuncLib::GetUiManager()->Hide("Equipment");
						}
					}
					else
					{
						IsEquippedItem = false;
						EquippedIndex = 0;
						LevelChanging = true;
						UGameplayFuncLib::SetAllPlayerUIControlState(EGameUIControlState::SecondaryUIState);
						PlayWidgetAnim("ShowMenuList",0.0f,1.0f,EUMGSequencePlayMode::Forward,1.0f,false);
					}
				}
			}
		}
	}
}

void UChangeEquipment::InitEquippmentTypeList()
{
	if(EquipmentMenuList->MenuList->GetNumItems() > 0)
	{
		for (int i = 0 ; i < EquipmentMenuList->MenuList->GetNumItems() ; ++i)
		{
			UMenuListEntry* TempEntry = EquipmentMenuList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(
				EquipmentMenuList->MenuList->GetItemAt(i));
			if(TempEntry)
			{
				TempEntry->EntryTypeName->SetVisibility(ESlateVisibility::Visible);
				TempEntry->Entry_Ground->SetRenderTranslation(FVector2D(-10.0f,0));
				
				UWidgetLayoutLibrary::SlotAsOverlaySlot(TempEntry->EntryName)->SetPadding(FMargin(0,15.0f,0,0));
			}
			else
			{
				TempEntry = EquipmentMenuList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(
				EquipmentMenuList->MenuList->GetItemAt(i));
				if(TempEntry)
				{
					TempEntry->EntryTypeName->SetVisibility(ESlateVisibility::Visible);
					TempEntry->Entry_Ground->SetRenderTranslation(FVector2D(-10.0f,0));
				
					UWidgetLayoutLibrary::SlotAsOverlaySlot(TempEntry->EntryName)->SetPadding(FMargin(0,15.0f,0,0));
				}
			}
		}
	}
	
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle1);
	
	this->SetRenderOpacity(1);
	PlayWidgetAnim("ShowMenuList",0.0f,1.0f,EUMGSequencePlayMode::Forward,1.0f,false);
}

void UChangeEquipment::InitPlayerInfoPanel()
{
	if(GameState)
	{
		const AAwCharacter* Me = GameState->GetMyCharacter();
		if(Me)
		{
			PlayerNameTextBlock->SetText(FText::FromString(Me->CharacterObj.Name));
			//暂时没有当前升级的最大经验值 先用 1000.0f 为所需经验值
			const float TempPercent = Me->CharacterObj.Exp / 1000.0f;
			CurExpTextBlock->SetText(FText::FromString(FString::FromInt(Me->CharacterObj.Exp)));
			NeedExpTextBlock->SetText(FText::FromString(FString::FromInt(1000)));
			EXP_Bar->SetPercent(TempPercent);

			EXP_VFX->SetRenderTranslation(FVector2D(EXP_Bar->GetWidgetStyle().FillImage.GetImageSize().X * TempPercent,
				EXP_VFX->GetRenderTransform().Translation.Y));

			PlayerLevel->SetText(FText::FromString(FString::FromInt(Me->CharacterObj.Level)));

			UTexture2D* ClassIcon = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(
				UGameplayFuncLib::GetAwDataManager()->GetClassIconById(Me->CharacterObj.ClassId).IconPath)); 
			
			if(ClassIcon)
			{
				ClassIconImage->SetBrushFromTexture(ClassIcon);
				ClassIconImage->SetDesiredSizeOverride(FVector2D(ClassIcon->GetSizeX(),ClassIcon->GetSizeY()));
			}
		}
	}
}

void UChangeEquipment::InitEquippmentItemList()
{
	if(ItemList->MenuList->GetNumItems() > 0)
	{
		for (int i = 0 ; i < ItemList->MenuList->GetNumItems() ; ++i)
		{
			UMenuListEntry* TempEntry = ItemList->MenuList->GetEntryWidgetFromItem<UMenuListEntry>(
				ItemList->MenuList->GetItemAt(i));
			if(IsValid(TempEntry))
			{
				TempEntry->PlayWidgetAnim("EffectRespiration",0.0f,0,EUMGSequencePlayMode::Forward,1.0f,false);
				TempEntry->EntryTypeName->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Equipped")));
				if(TempEntry->GetEntryData()->IsEquipped)
				{
					TempEntry->EntryTypeName->SetVisibility(ESlateVisibility::Visible);
					UTexture2D* TemTexture =
						LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->
								GetItemIconById(TempEntry->GetEntryData()->ItemId).Path));
					if(TemTexture)
					{
						ItemImage->SetBrushFromTexture(TemTexture);
						ItemImage->SetDesiredSizeOverride(FVector2D(TemTexture->GetSizeX(),TemTexture->GetSizeY()));
					}
				}
				else
				{
					TempEntry->EntryTypeName->SetVisibility(ESlateVisibility::Collapsed);
				}
				TempEntry->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
				UWidgetLayoutLibrary::SlotAsOverlaySlot(TempEntry->EntryTypeName)->SetPadding(FMargin(-10.0f,0,0,0));
				UWidgetLayoutLibrary::SlotAsOverlaySlot(TempEntry->MakeIconOverlay)->SetPadding(FMargin(0,0,55.0f,15.0f));
			}
		}
	}
	if(IsValid(ItemList->GetSelectListEntry()))
		ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle1);
}

void UChangeEquipment::SetItemImage()
{
	if(IsValid(ItemList->GetSelectListEntry()))
	{
		UTexture2D* TemTexture =
						LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->
								GetItemIconById(ItemList->GetSelectListEntry()->GetEntryData()->ItemId).Path));
		if(TemTexture)
		{
			ItemImage->SetBrushFromTexture(TemTexture);
			ItemImage->SetDesiredSizeOverride(FVector2D(TemTexture->GetSizeX(),TemTexture->GetSizeY()));
		}
	}
	
}

void UChangeEquipment::SetMosaicGroove(int Index, UMosaicGroove* MosaicGroove)
{
	if (ItemList->GetSelectData()->Affix.Num() >= Index + 1)
	{
		MosaicGroove->SetVisibility(ESlateVisibility::HitTestInvisible);
		const FEquipmentSet TempSet = UGameplayFuncLib::GetAwDataManager()->GetEquipSetById(ItemList->GetSelectData()->Affix[Index]);
		if (TempSet.Id.IsEmpty())
			MosaicGroove->SetVisibility(ESlateVisibility::Collapsed);
		MosaicGroove->SetMosaicAffixIconAndMosaicAffix(UGameplayFuncLib::GetAwDataManager()->
			GetItemIconById(TempSet.StoneId).Path,UGameplayFuncLib::GetAwDataManager()->GetTextByKey(TempSet.EquipmentSet));
	}
	else
		MosaicGroove->SetVisibility(ESlateVisibility::Collapsed);
}



// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "ExplainUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UExplainUI : public UBaseUI
{
	GENERATED_BODY()


	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Explain_Icon;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* Explain_Name;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* Explain_Describe;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* Count_TextBlock;


	UFUNCTION(BlueprintCallable)
	void SetAttribute(UTexture2D* Icon,FString Name,FString Describe,int Count);
};

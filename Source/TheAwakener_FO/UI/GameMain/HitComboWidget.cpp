// Fill out your copyright notice in the Description page of Project Settings.


#include "HitComboWidget.h"

#include "Kismet/KismetSystemLibrary.h"

void UHitComboWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	for (int i = 0; i < 9; i++)
	{
		UTextBlock* ThisText = Cast<UTextBlock>(GetWidgetFromName(FName(FString("HitsCombo").Append(FString::FromInt(i)))));
		if (ThisText)
		{
			this->ComboText.Add(ThisText);
			ThisText->SetVisibility(ESlateVisibility::Hidden);
			ThisText->SetRenderScale(FVector2D::ZeroVector);
		}
	}

	for (int i = 0; i < 4; i ++)
	{
		UTextBlock* ThisText = Cast<UTextBlock>(GetWidgetFromName(FName(FString("HitsNumber").Append(FString::FromInt(i)))));
		this->NumberText.Add(ThisText);
		if (ThisText)
		{
			ThisText->SetVisibility(ESlateVisibility::Hidden);
			ThisText->SetRenderScale(FVector2D::ZeroVector);
		}
	}

	this->ShowingStep = 0;
	this->TimeElapsed = 0;
}

void UHitComboWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	bCanUpdate = true;
	this->TimeElapsed += InDeltaTime;

	switch (this->ShowingStep)
	{
	case 1:
		{
			constexpr float EachBeginElapsed = 0.05f;		//每个间隔0.1秒出现
			constexpr float EachDoneAppear = 0.12f;	//每个完成旋转的时间
			//字母和数字挨个出来
			for (int i = 0; i < this->ComboText.Num(); i++)
			{
				const bool ToShow = TimeElapsed >= i * EachBeginElapsed;
				if (ToShow == false)
				{
					//还没轮到您显示呢
					this->ComboText[i]->SetVisibility(ESlateVisibility::Hidden);
					this->ComboText[i]->SetRenderScale(FVector2D::ZeroVector);
					this->ComboText[i]->SetOpacity(0);
					continue;
				}
				const float CurRate = FMath::Clamp((TimeElapsed - i * EachBeginElapsed) / EachDoneAppear, 0.f, 1.f);
				this->ComboText[i]->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				this->ComboText[i]->SetRenderScale(FVector2D(CurRate,CurRate));
				this->ComboText[i]->SetOpacity(CurRate);
			}
			for (int i = 0; i < this->NumberText.Num(); i++)
			{
				const bool ToShow = ToShowNumber(i) && TimeElapsed >= (i + 9) * EachBeginElapsed;
				if (ToShow == false)
				{
					//还没轮到您显示呢
					this->NumberText[i]->SetVisibility(ESlateVisibility::Hidden);
					this->NumberText[i]->SetRenderScale(FVector2D::ZeroVector);
					this->NumberText[i]->SetOpacity(0);
					continue;
				}
				const float CurRate = FMath::Clamp((TimeElapsed - (i + 9) * EachBeginElapsed) / EachDoneAppear, 0.f, 1.f);
				this->NumberText[i]->SetVisibility(ESlateVisibility::SelfHitTestInvisible);
				this->NumberText[i]->SetRenderScale(FVector2D(CurRate,CurRate));
				this->NumberText[i]->SetOpacity(CurRate);
			}

			//显示完毕，进入状态2
			if (ToShowTextCount() * EachBeginElapsed + EachDoneAppear <= TimeElapsed)
			{
				ShowingStep = 2;
				TimeElapsed = 0;
			}
		}break;
	case 2:
		{
			//稳定显示的状态，也许会有个倒计时，所以内容还是写在这里先，看到时候怎么加倒计时
			constexpr float CurRate = 1;
			for (int i = 0; i < this->NumberText.Num(); i++)
			{
				const bool ToShow = ToShowNumber(i);
				this->NumberText[i]->SetVisibility(ToShow ? ESlateVisibility::SelfHitTestInvisible : ESlateVisibility::Hidden);
				this->NumberText[i]->SetRenderScale(FVector2D::UnitVector);
				this->NumberText[i]->SetOpacity(CurRate);
			}
			if (TimeElapsed >= ComboEndsAfterSec)
			{
				//结束了，摧毁吧
				ShowingStep = 4;
				TimeElapsed = 0;
				CurrentComboNumber = 0;
			}
		}break;
	case 3:
		{
			//发生了变化
			const int ToBiggerTextCount = this->CurrentComboIncreased >=1000 ? 4 : (
				this->CurrentComboIncreased >= 100 ? 3 : (
					this->CurrentComboIncreased >= 10 ? 2 : 1
				)
			);
			constexpr float BiggerInSec = 0.09f;
			constexpr float ReturnInSec = 0.06f;
			constexpr float Enlarge = 0.5f;
			if (TimeElapsed <= BiggerInSec)
			{
				//放大
				const float CurRate = TimeElapsed / BiggerInSec * Enlarge + 1;
				for (int i = 0; i < ToBiggerTextCount; i++)
				{
					this->NumberText[i]->SetRenderScale(FVector2D(CurRate, CurRate));
				}
			}else if (TimeElapsed <= BiggerInSec + ReturnInSec)
			{
				//然后缩小
				const float CurRate = 1.0f + Enlarge - (TimeElapsed - BiggerInSec) / ReturnInSec * Enlarge;
				for (int i = 0; i < ToBiggerTextCount; i++)
				{
					this->NumberText[i]->SetRenderScale(FVector2D(CurRate, CurRate));
				}
			}else
			{
				for (int i = 0; i < NumberText.Num(); i++)
				{
					this->NumberText[i]->SetRenderScale(FVector2D::UnitVector);
				}
				//回到2状态
				ShowingStep = 2;
				TimeElapsed = 0;
			}
		}break;
	case 4:
		{
			//该摧毁了 TODO: 先直接消灭了
			float InSec = 0;	//所以这个是0秒内完成

			if (TimeElapsed >= InSec)
			{
				this->ShowingStep = 0;
				this->TimeElapsed = 0;
				for (int i = 0; i < this->ComboText.Num(); i++)
				{
					this->ComboText[i]->SetVisibility(ESlateVisibility::Hidden);
					this->ComboText[i]->SetRenderScale(FVector2D::ZeroVector);
					this->ComboText[i]->SetOpacity(0);
				}
				for (int i = 0; i < this->NumberText.Num(); i++)
				{
					this->NumberText[i]->SetVisibility(ESlateVisibility::Hidden);
					this->NumberText[i]->SetRenderScale(FVector2D::ZeroVector);
					this->NumberText[i]->SetOpacity(0);
				}
				
			}
		}break;
		default:break;
	}

}

bool UHitComboWidget::ToShowNumber(int Index) const
{
	return this->CurrentComboNumber >= FMath::Pow(10, Index*1.0);
}

int UHitComboWidget::ToShowTextCount() const
{
	int Count = 0;
	while (ToShowNumber(Count) && Count < 4)
	{
		Count += 1;
	}
	return Count + 1 + 9;	//+1是长度，+9是HitsCombo
}

void UHitComboWidget::ShowNumber(int Number, float ComboEndsInSec)
{
	if (!bCanUpdate)return;
	this->CurrentComboIncreased = Number - CurrentComboNumber;
	if (this->CurrentComboIncreased <= 0) return;	//小于等于0的是开玩笑的，别鸟他
	this->CurrentComboNumber = Number;
	this->ComboEndsAfterSec = ComboEndsInSec;
	
	SetNumberTexts();
	
	if (this->ShowingStep <= 0 || this->ShowingStep >= 4)
	{
		//如果还没开始播放，或者完蛋了，那么就开始播放
		TimeElapsed = 0;
		ShowingStep = 1;
	}else if (this->ShowingStep == 2 || this->ShowingStep == 3)
	{
		//如果已经在屏幕上，就直接进状态3，开始修改数字，每次都从3重新开始
		TimeElapsed = 0;
		ShowingStep = 3;
	}
}

void UHitComboWidget::SetNumberTexts()
{
	for (int i = 0; i < this->NumberText.Num(); i++)
	{
		const int ToDiv = FMath::Pow(10, i*1.0);
		const int Val = FMath::FloorToInt(CurrentComboNumber*1.f / ToDiv) % 10;
		this->NumberText[i]->SetText(FText::FromString(FString::FromInt(Val)));
	}
}
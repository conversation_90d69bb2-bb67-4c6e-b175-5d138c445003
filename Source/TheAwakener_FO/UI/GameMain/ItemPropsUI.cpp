// Fill out your copyright notice in the Description page of Project Settings.


#include "ItemPropsUI.h"

#include "Engine/Texture2D.h"

void UItemPropsUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	ItemProps_Ground			=	Cast<UImage>(GetWidgetFromName("ItemPropsGround"));
	ItemProps_Icon				=	Cast<UImage>(GetWidgetFromName("ItemPropsIcon"));
	NumberIconImage				=	Cast<UImage>(GetWidgetFromName("NumberIcon"));

	ItemProps_Icon->SetVisibility(ESlateVisibility::Collapsed);
}

void UItemPropsUI::NativeConstruct()
{
	Super::NativeConstruct();
	if(IsValid(NumberIconImage) && IsValid(NumberImage))
	{
		NumberIconImage->SetBrushFromTexture(NumberImage);
		NumberIconImage->SetDesiredSizeOverride(FVector2D(NumberImage->GetSizeX(),NumberImage->GetSizeY()));
	}
		
}

void UItemPropsUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UItemPropsUI::SetAttribute(FString IconPath)
{
	UTexture2D* Icon = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(IconPath));
	if(Icon)
	{
		ItemProps_Icon->SetBrushFromTexture(Icon);
		ItemProps_Icon->SetDesiredSizeOverride(FVector2D(Icon->GetSizeX(),Icon->GetSizeY()));
	}
}

void UItemPropsUI::UpdateView()
{
	if(IsHaveQuickProps)
	{
		ItemProps_Icon->SetVisibility(ESlateVisibility::Visible);
	}
	else
	{
		ItemProps_Icon->SetVisibility(ESlateVisibility::Collapsed);
	}
}


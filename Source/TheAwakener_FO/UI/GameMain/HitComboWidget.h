// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "UObject/Object.h"
#include "HitComboWidget.generated.h"

/**
 * Hits Combo 显示器
 */
UCLASS()
class THEAWAKENER_FO_API UHitComboWidget : public UUserWidget
{
	GENERATED_BODY()
private:
	//Combo字符
	UPROPERTY()
	TArray<UTextBlock*> ComboText;

	//数字字符
	UPROPERTY()
	TArray<UTextBlock*> NumberText;

	/**
	 *当前显示的步骤
	 *0=尚未显示
	 *1=开始显示，出现的动画，此时Combo数变化不影响数字
	 *2=完全显示，所以Combo数字变化会有效果
	 *3=完全显示时数字发生变化
	 *4=完全消退过程，Combo数字出来会重新回到1，否则回到0
	 */
	UPROPERTY()
	int ShowingStep = 0;

	//播放当前动画的进展
	UPROPERTY()
	float TimeElapsed = 0;

	//当前连击增长了多少（很可能一次+超过1的数字）
	UPROPERTY()
	int CurrentComboIncreased = 0;
	//当前应当播放的连击数字
	UPROPERTY()
	int CurrentComboNumber = 0;

	//多久之后Combo就会消退掉
	UPROPERTY()
	float ComboEndsAfterSec = 1;
	bool bCanUpdate;

	//当前第n个数字该不该显示，根据CurrentComboNumber算出来的
	bool ToShowNumber(int Index) const;

	//当前一共要显示多少个字符（含“hitscombo”这9个）
	int ToShowTextCount() const;

	//设置每个数字Text，就是偷懒，别废话了
	void SetNumberTexts();
public:
	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

	//显示数字
	UFUNCTION(BlueprintCallable)
	void ShowNumber(int Number, float ComboEndsInSec = 1);
};

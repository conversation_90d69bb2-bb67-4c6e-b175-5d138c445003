// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeSkill.h"

#include "ButtonUI.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UChangeSkill::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	MyBackground_Blur						=	Cast<UBackgroundBlur>(GetWidgetFromName("BackgroundBlurBase"));

	FrenchRingBaseCanvasPanel				=	Cast<UCanvasPanel>(GetWidgetFromName("FrenchRingBase"));

	BigShockWaveImage						=	Cast<UImage>(GetWidgetFromName("BigShockWave"));
	SmallShockWaveImage						=	Cast<UImage>(GetWidgetFromName("SmallShockWave"));
	BigTriangleImage						=	Cast<UImage>(GetWidgetFromName("BigTriangle"));
	SmallTriangleImage						=	Cast<UImage>(GetWidgetFromName("SmallTriangle"));
	EightStarImage							=	Cast<UImage>(GetWidgetFromName("EightStar"));
	CircletImage							=	Cast<UImage>(GetWidgetFromName("Circlet"));
	CipherImage								=	Cast<UImage>(GetWidgetFromName("Cipher"));
	ButtonIndication						=	Cast<UButtonIndication>(GetWidgetFromName("WBP_ButtonIndication"));
	
	TArray<FName> RouletteWBPName;
	RouletteWBPName.Add("WBP_MainRoulette");
	RouletteWBPName.Add("WBP_SecondRoulette");
	RouletteWBPName.Add("WBP_ThirdRoulette");
	for (int i = 0; i < RouletteWBPName.Num(); i++)
	{
		URoulette* ThisRoulette = Cast<URoulette>(GetWidgetFromName(RouletteWBPName[i]));
		MyRoulette.Add(ThisRoulette);
		if (ThisRoulette)
		{
			ThisRoulette->SetVisibility(ESlateVisibility::Visible);
			ThisRoulette->Roulette_Image->SetVisibility(ESlateVisibility::Collapsed);
		}
	}
	
	
	
	//MySecondRoulette->SetVisibility(ESlateVisibility::Collapsed);
	//MyThirdRoulette->SetVisibility(ESlateVisibility::Collapsed);

	ActionSelections = UGameplayFuncLib::GetAwDataManager()->
					GetActionSelectionsByClassId(UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->PlayerClassId);
	
}

void UChangeSkill::NativeConstruct()
{
	Super::NativeConstruct();

	GameState = UGameplayFuncLib::GetAwGameState();

	FrenchRingRotateSpeed 			=	0.07f;
	OptionsBarRotateSpeed 			=	-0.12f;
	AcceleratedSpeed				=	1.0f;
	MethodOfRingState				=	EMethodOfRingState::None;
	
	
	UButtonUI* TempButton = Cast<UButtonUI>(UGameplayFuncLib::GetUiManager()->Show("ButtonUI"));
	TempButton->SetButton(UGameplayFuncLib::GetAwDataManager()->GetKeyMappingById("Key_Left").
		Gamepad_Icon,UGameplayFuncLib::GetAwDataManager()->GetTextByKey("UD"));

	UButtonUI* TempButton1 = Cast<UButtonUI>(UGameplayFuncLib::GetUiManager()->Show("ButtonUI"));
	TempButton1->SetButton(UGameplayFuncLib::GetAwDataManager()->GetKeyMappingById("Key_Cross").
		Gamepad_Icon,UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Back"));

	UButtonUI* TempButton2 = Cast<UButtonUI>(UGameplayFuncLib::GetUiManager()->Show("ButtonUI"));
	TempButton2->SetButton(UGameplayFuncLib::GetAwDataManager()->GetKeyMappingById("Key_Eclipse").
		Gamepad_Icon,UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Next"));

	UButtonUI* TempButton3 = Cast<UButtonUI>(UGameplayFuncLib::GetUiManager()->Show("ButtonUI"));
	TempButton3->SetButton(UGameplayFuncLib::GetAwDataManager()->GetKeyMappingById("Key_Left").
		Gamepad_Icon,UGameplayFuncLib::GetAwDataManager()->GetTextByKey("LR"));
	TempButton3->ButtonIconImage->SetRenderTransformAngle(90.0f);

	
	ButtonIndication->ButtonIndicationHorizontalBox->AddChildToHorizontalBox(TempButton);
	ButtonIndication->ButtonIndicationHorizontalBox->AddChildToHorizontalBox(TempButton3);
	ButtonIndication->ButtonIndicationHorizontalBox->AddChildToHorizontalBox(TempButton1);
	ButtonIndication->ButtonIndicationHorizontalBox->AddChildToHorizontalBox(TempButton2);
	
}

void UChangeSkill::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	MethodOfRingRotate();
	switch (MethodOfRingState)
	{
	case EMethodOfRingState::None:
		{
			if (GameState)
			{
				const AAwCharacter* Me = GameState->GetMyCharacter();
				if(Me->GetCmdComponent()->IsActionOccur("ChangeSkillOrClass_Up") ||
					Me->GetCmdComponent()->IsActionOccur("ChangeSkillOrClass_Up",EAwInputState::Hold))
				{
					if(MethodOfRingState == EMethodOfRingState::None)
						RouletteRotateUp();

					//RefreshLayerItemInfo(FocusedInRoulette());
				}
				else if(Me->GetCmdComponent()->IsActionOccur("ChangeSkillOrClass_Down") ||
					Me->GetCmdComponent()->IsActionOccur("ChangeSkillOrClass_Down",EAwInputState::Hold))
				{
					if(MethodOfRingState == EMethodOfRingState::None)
						RouletteRotateDown();

					//RefreshLayerItemInfo(FocusedInRoulette());
				}
				else
				{
					RouletteStopRotate();
				}
				if(Me->GetCmdComponent()->IsActionOccur("ChangeSkillOrClass_Confirm",EAwInputState::Press,true) )
				{
					
					if(MethodOfRingState == EMethodOfRingState::None)
					{
						switch (LayerIndex)
						{
						case 0:
							{
								if(this->LayerItemInfo[1].RouletteItemInfos.Num() > 0)
								{
									UGameplayFuncLib::PlayUIAudio("ConfirmKey_Next");
									IsConfirm = true;
									RouletteConfirm();
								}
								else
								{
									UGameplayFuncLib::PlayUIAudio("ConfirmKey_No");
								}
								break;
							}
						case 1:
							{
								if(MyRoulette[1]->RouletteItemInfos.Num() > 0 &&
								UGameplayFuncLib::GetAwDataManager()->GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
							GetActionLinkByMainActionId(*MyRoulette[1]->GetCurSelectItemInfo().InfoKey).Id).CandidateActions.Num() > 0)
								{
									UGameplayFuncLib::PlayUIAudio("ConfirmKey_Next");
									IsConfirm = true;
									RouletteConfirm();
								}
								else
								{
									UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
									RouletteConfirm();
								}
							
								break;
							}
						case 2:
							{
								UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
								RouletteConfirm();
								break;
							}
						}
						
					}
				}
				if(Me->GetCmdComponent()->IsActionOccur("ChangeSkillOrClass_Refuse",EAwInputState::Press,true) || Me->GetCmdComponent()->
					IsActionOccur("ChangeSkillOrClass_Refuse",EAwInputState::Press,true))
				{
					
					if(MethodOfRingState == EMethodOfRingState::None)
					{
						UGameplayFuncLib::PlayUIAudio("ConfirmKey_Back");
						IsConfirm = false;
						switch (LayerIndex)
						{
						case 0:
							{ 
								MethodOfRingRotateAccelerate();
								
								break;
							}
						case 1:
							{
								RouletteBack();
								break;
							}
						case 2:
							{
								RouletteBack();
								break;
							}
						}
					}
				}
	
			}
			
			break;
		}
	case EMethodOfRingState::Collapsing:
		{
			MethodOfRingCollapsing(InDeltaTime);
			break;
		}
	case EMethodOfRingState::Unfolding:
		{
			MethodOfRingUnfold(InDeltaTime);
			break;
		}
	case EMethodOfRingState::IsAccelerating:
		{
			
			break;
		}
	}
	
}

void UChangeSkill::SetSkillToRoulette()
{
	//基本配置
	TArray<float> RouletteAngle;
		RouletteAngle.Add(16.0f);
		RouletteAngle.Add(12.0f);
		RouletteAngle.Add(9.5f);
	TArray<float> RouletteDiameter;
		RouletteDiameter.Add(725.0f);
		RouletteDiameter.Add(965.0f);
		RouletteDiameter.Add(1205.0f);
	
	//数据
	TArray<int> Idx;
	Idx.Init(0, 3);
	this->RefreshLayerItemInfo(Idx);
	
	for (int i =0 ;i < 3; i++)
	{
		FRouletteInfo Test = FRouletteInfo();
		Test.Angle = RouletteAngle[i];
		Test.Diameter = RouletteDiameter[i];
		Test.PointCount = 7;
		Test.CompensationValue = 3.0f;
		Test.ItemPopupDir = EItemPopupDir::Right;
		Test.RouletteStyle = ERouletteStyle::RouletteLimited;

		MyRoulette[i]->SetRoulette(Test,this->LayerItemInfo[i].RouletteItemInfos);
	}
	ChangeConfirmName();
}

//
// void UChangeSkill::SetSkillToMainRoulette()
// {
// 	
// 	TArray<FActionSelection> ActionSelections = UGameplayFuncLib::GetAwDataManager()->
// 	GetActionSelectionsByClassId(UGameplayFuncLib::GetAwGameState()->MyCharacter->PlayerClassId);
// 	
// 	
// 	TArray<FRouletteItemInfo> TempArray;
// 	FRouletteInfo Test = FRouletteInfo();
// 	Test.Angle = 16.0f;
// 	Test.Diameter = 725.0f;
// 	Test.PointCount = 7;
// 	Test.CompensationValue = 3.0f;
// 	Test.ItemPopupDir = EItemPopupDir::Right;
// 	Test.RouletteStyle = ERouletteStyle::RouletteLimited;
//
// 	if(ActionSelections.Num() > 0)
// 	{
// 		for (auto action : ActionSelections)
// 		{
// 			FRouletteItemInfo TempItemInfo = FRouletteItemInfo();
// 			FString CurMainAction = UGameplayFuncLib::GetAwGameState()->MyCharacter->GetActionComponent()->CurrentSelectedMainActionId(action);
// 		
// 			TempItemInfo.ActionSelection		= action;
// 			TempItemInfo.ActionSelectionUIInfo	= UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(action.Id);
// 			for (auto temp : UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(action.Id).Actions)
// 			{
// 				if(temp.ActionId == CurMainAction)
// 				{
// 					TempItemInfo.ActionId	= temp.ActionId;
// 					TempItemInfo.IconPath	= temp.Icon;
// 					TempItemInfo.Name		= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(temp.ActionId);
// 				}
// 			}
// 			TempArray.Add(TempItemInfo);
// 		}
// 	
// 			
// 		MyMainRoulette->SetRoulette(Test,TempArray);
//
// 		ChangeConfirmName();
// 	}
// 	
// }
//
// void UChangeSkill::SetSkillToSecondRoulette()
// {
// 	
// 	TArray<FRouletteItemInfo> TempArray;
// 	FRouletteInfo Test = FRouletteInfo();
// 	int i = 0;
// 	Test.Angle = 12.0f;
// 	Test.Diameter = 965.0f;
// 	Test.PointCount = 7;
// 	Test.CompensationValue = 3.0f;
// 	Test.ItemPopupDir = EItemPopupDir::Right;
// 	Test.RouletteStyle = ERouletteStyle::RouletteLimited;
// 	TArray<FActionSelectionMainActionUIInfo> ActionSelectionMainActionUIInfo = MyMainRoulette->GetCurSelectItemInfo().ActionSelectionUIInfo.Actions;
// 	
// 	if(ActionSelectionMainActionUIInfo.Num() > 0)
// 	{
// 		for (auto Temp : ActionSelectionMainActionUIInfo)
// 		{
// 			++i;
// 			FRouletteItemInfo TempItemInfo			= FRouletteItemInfo();
// 		
// 			TempItemInfo.ActionSelection			= MyMainRoulette->GetCurSelectItemInfo().ActionSelection;
// 			TempItemInfo.ActionId					= Temp.ActionId;
// 			TempItemInfo.IconPath					= Temp.Icon;
// 			TempItemInfo.Name						= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.ActionId);
// 			TempItemInfo.IsShowItemDecorateGround	= false;
// 			if(Temp.ActionId == MyMainRoulette->GetCurSelectItemInfo().ActionId)
// 			{
// 				TempItemInfo.IsEquipped				= true;
// 				TempArray.Insert(TempItemInfo,0);
// 			}
// 			else
// 			{
// 				TempItemInfo.IsEquipped				= false;
// 				TempArray.Add(TempItemInfo);
// 			}
// 		
// 		}
// 		MySecondRoulette->SetRoulette(Test,TempArray);
// 	
// 		ChangeConfirmName();
// 	}
// 	
// }
//
// void UChangeSkill::SetSkillToThirdRoulette()
// {
// 	TArray<FRouletteItemInfo> TempArray;
// 	FRouletteInfo Test = FRouletteInfo();
// 	Test.Angle = 8.0f;
// 	Test.Diameter = 1205.0f;
// 	Test.PointCount = 7;
// 	Test.CompensationValue = 3.0f;
// 	Test.ItemPopupDir = EItemPopupDir::Right;
// 	Test.RouletteStyle = ERouletteStyle::RouletteLimited;
//
// 	UActionComponent* ActionComp = UGameplayFuncLib::GetAwGameState()->MyCharacter->GetActionComponent();
// 	
// 	FString CurrentLinkedSelectActionId = ActionComp->CurrentLinkedSelectActionId(
// 		UGameplayFuncLib::GetAwDataManager()->GetActionLinkByMainActionId(*MySecondRoulette->GetCurSelectItemInfo().ActionId));
//
// 	FString OriActionId = ActionComp->GetActionById(CurrentLinkedSelectActionId)->OriginId;
//
// 	TArray<FActionLinkCandidateActionUIInfo> ActionLinkCandidateActionUIInfos = UGameplayFuncLib::GetAwDataManager()->
// 	GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
// 		GetActionLinkByMainActionId(*MySecondRoulette->GetCurSelectItemInfo().ActionId).Id).CandidateActions;
// 	
// 	if(ActionLinkCandidateActionUIInfos.Num() > 0)
// 	{
// 		for (FActionLinkCandidateActionUIInfo Temp : ActionLinkCandidateActionUIInfos)
// 		{
// 			FRouletteItemInfo TempItemInfo			= FRouletteItemInfo();
//
// 			TempItemInfo.ActionId					= Temp.ActionId;
// 			TempItemInfo.IconPath					= Temp.Icon;
// 			TempItemInfo.Name						= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.ActionId);
// 			TempItemInfo.IsShowItemDecorateGround	= false;
//
//
// 			if( Temp.ActionId == OriActionId)
// 			{
// 				TempItemInfo.IsEquipped				= true;
// 				TempArray.Insert(TempItemInfo,0);
// 			}
// 			else
// 			{
// 				TempItemInfo.IsEquipped				= false;
// 				TempArray.Add(TempItemInfo);
// 			}
// 		
// 		}
//
// 		MyThirdRoulette->SetRoulette(Test,TempArray);
// 	
// 		ChangeConfirmName();
// 	}
// 	
// }

void UChangeSkill::RouletteRotateUp()
{
	if(MyRoulette[LayerIndex]->GetCurFocusIndex() <= 0)
		return;
	if(MyRoulette[LayerIndex])
	{
		if(MyRoulette[LayerIndex]->RouletteState == ERouletteState::Select)
		{
			MyRoulette[LayerIndex]->RouletteUpRotate();
			MyRoulette[LayerIndex]->ResetRotateSpeed();
			UpdateSkill();
		}
		else
		{
			MyRoulette[LayerIndex]->ChangeRotateSpeed();
		}
	}
	ChangeConfirmName();
	//SetSkillToRoulette();
	//
	// switch (MenuHierarchy)
	// {
	// case EMenuHierarchy::MainLevel:
	// 	{
	// 		if(MyMainRoulette)
	// 		{
	// 			if(MyMainRoulette->RouletteState == ERouletteState::Select)
	// 			{
	// 				MyMainRoulette->RouletteUpRotate();
	// 				MyMainRoulette->ResetRotateSpeed();
	// 			}
	// 			else
	// 			{
	// 				MyMainRoulette->ChangeRotateSpeed();
	// 			}
	// 		}
	// 		break;
	// 	}
	// case EMenuHierarchy::LevelTwo:
	// 	{
	// 		if(MySecondRoulette)
	// 		{
	// 			if(MySecondRoulette->RouletteState == ERouletteState::Select)
	// 			{
	// 				MySecondRoulette->RouletteUpRotate();
	// 				MySecondRoulette->ResetRotateSpeed();
	// 			}
	// 			else
	// 			{
	// 				MySecondRoulette->ChangeRotateSpeed();
	// 			}
	// 		}
	// 		break;
	// 	}
	// case EMenuHierarchy::TierThree:
	// 	{
	// 		if(MyThirdRoulette)
	// 		{
	// 			if(MyThirdRoulette->RouletteState == ERouletteState::Select)
	// 			{
	// 				MyThirdRoulette->RouletteUpRotate();
	// 				MyThirdRoulette->ResetRotateSpeed();
	// 			}
	// 			else
	// 			{
	// 				MyThirdRoulette->ChangeRotateSpeed();
	// 			}
	// 		}
	// 		break;
	// 	}
	// }
	//ChangeConfirmName();
}

void UChangeSkill::RouletteRotateDown()
{
	if(MyRoulette[LayerIndex]->GetCurFocusIndex() >= MyRoulette[LayerIndex]->GetCreateCount() - 1)
		return;
	if(MyRoulette[LayerIndex])
	{
		if(MyRoulette[LayerIndex]->RouletteState == ERouletteState::Select)
		{
			MyRoulette[LayerIndex]->RouletteDownRotate();
			MyRoulette[LayerIndex]->ResetRotateSpeed();
			UpdateSkill();
		}
		else
		{
			MyRoulette[LayerIndex]->ChangeRotateSpeed();
		}
	}
	ChangeConfirmName();
	//SetSkillToRoulette();
	// switch (MenuHierarchy)
	// {
	// case EMenuHierarchy::MainLevel:
	// 	{
	// 		if(MyMainRoulette)
	// 		{
	// 			if(MyMainRoulette->RouletteState == ERouletteState::Select)
	// 			{
	// 				MyMainRoulette->RouletteDownRotate();
	// 				MyMainRoulette->ResetRotateSpeed();
	// 			}
	// 			else
	// 			{
	// 				MyMainRoulette->ChangeRotateSpeed();
	// 			}
	// 		}
	// 			
	// 		break;
	// 	}
	// case EMenuHierarchy::LevelTwo:
	// 	{
	// 		if(MySecondRoulette)
	// 		{
	// 			MySecondRoulette->RouletteDownRotate();
	// 			MySecondRoulette->ResetRotateSpeed();
	// 		}
	// 		else
	// 		{
	// 			MySecondRoulette->ChangeRotateSpeed();
	// 		}
	// 		break;
	// 	}
	// case EMenuHierarchy::TierThree:
	// 	{
	// 		if(MyThirdRoulette)
	// 		{
	// 			MyThirdRoulette->RouletteDownRotate();
	// 			MyThirdRoulette->ResetRotateSpeed();
	// 		}
	// 		else
	// 		{
	// 			MyThirdRoulette->ChangeRotateSpeed();
	// 		}
	// 		break;
	// 	}
	// }
	//ChangeConfirmName();
}

void UChangeSkill::RouletteConfirm()
{
	switch (LayerIndex)
	{
	case 0:
		{
			if(this->LayerItemInfo[1].RouletteItemInfos.Num() > 0)
			{
				MyRoulette[LayerIndex]->SetIsRouletteFocus(false);
				if (LayerIndex < 2) LayerIndex += 1;
				MyRoulette[LayerIndex]->SetIsRouletteFocus(true);
			}
			break;
		}
	case 1:
		{
			if(MyRoulette[1]->RouletteState == ERouletteState::Select)
			{
				MyRoulette[LayerIndex]->ResetRotateSpeed();
				
				UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActionComponent()->ChangeMainAction(
						ActionSelections[MyRoulette[0]->GetCurFocusIndex()],MyRoulette[1]->GetCurSelectItemInfo().InfoKey);
					
				FRouletteItemInfo TempItemInfo;

				TempItemInfo.InfoKey		= MyRoulette[1]->GetCurSelectItemInfo().InfoKey;
				TempItemInfo.IconPath		= MyRoulette[1]->GetCurSelectItemInfo().IconPath;
				TempItemInfo.Name			= MyRoulette[1]->GetCurSelectItemInfo().Name;
				TempItemInfo.ButtonIconPath = MyRoulette[1]->GetCurSelectItemInfo().ButtonIconPath;
					
				MyRoulette[0]->SetCurSelectItemInfo(TempItemInfo);
				MyRoulette[0]->GetCurSelectItem()->SetRouletteItemAttributes(MyRoulette[0]->GetCurSelectItemInfo());
				
				if(MyRoulette[1]->RouletteItemInfos.Num() > 0 &&
			UGameplayFuncLib::GetAwDataManager()->GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
			GetActionLinkByMainActionId(*MyRoulette[1]->GetCurSelectItemInfo().InfoKey).Id).CandidateActions.Num() > 0)
				{
					MyRoulette[LayerIndex]->SetIsRouletteFocus(false);
					if (LayerIndex < 2) LayerIndex += 1;
					MyRoulette[LayerIndex]->SetIsRouletteFocus(true);
				}
			}
			else if(MyRoulette[1]->RouletteState == ERouletteState::Turning)
			{
				MyRoulette[1]->ChangeRotateSpeed();
			}

			if(!MyRoulette[1]->GetCurSelectItem()->IsSelected)
			{
				MyRoulette[1]->GetCurEquippedItem()->SetRouletteItemIsEquipped(false);
				
				MyRoulette[1]->GetCurSelectItem()->SetRouletteItemIsEquipped(true);
			}
			
			break;
		}
	case 2:
		{
			if(MyRoulette[2]->RouletteState == ERouletteState::Select)
			{
				if(!MyRoulette[2]->GetCurSelectItem()->IsSelected)
				{
					MyRoulette[2]->GetCurEquippedItem()->SetRouletteItemIsEquipped(false);
					
					MyRoulette[2]->GetCurSelectItem()->SetRouletteItemIsEquipped(true);
					
					UActionComponent* ActionComp = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActionComponent();
					FActionLink ActionLink = UGameplayFuncLib::GetAwDataManager()->
					GetActionLinkByMainActionId(*MyRoulette[1]->GetCurSelectItemInfo().InfoKey); 
					
					const FString ChangeToId = MyRoulette[2]->GetCurSelectItemInfo().InfoKey;
					FActionInfo* ToAction = ActionComp->GetActionByOriginId(ChangeToId);
					const FString ToId = ToAction ? ToAction->Id : ChangeToId;
					
					ActionComp->ChangeLinkedAction(ActionLink, ToId);
					const FString ChangedId = ActionComp->CurrentLinkedSelectActionId(ActionLink);
				}
			}
			break;
		}
	}
	ChangeConfirmName();
	//SetSkillToRoulette();
	
	// switch (MenuHierarchy)
	// {
	// case EMenuHierarchy::MainLevel:
	// 	{
	// 		if(MyMainRoulette->GetCurSelectItemInfo().ActionSelectionUIInfo.Actions.Num() > 0)
	// 		{
	// 			if(MyMainRoulette->RouletteState == ERouletteState::Select)
	// 			{
	// 				MyMainRoulette->SetRouletteState(ERouletteState::Confirm);
	// 				MyMainRoulette->ResetRotateSpeed();
	// 				for (auto Temp : MyMainRoulette->RouletteItemArray)
	// 				{
	// 					if(MyMainRoulette->GetCurSelectItem() == Temp)
	// 					{
	// 						Temp->RouletteItem_Ground->SetVisibility(ESlateVisibility::Collapsed);
	// 						Temp->RouletteItem_IconGround->SetVisibility(ESlateVisibility::Collapsed);
	// 					}
	// 					else
	// 					{
	// 						if(Temp->GetVisibility() == ESlateVisibility::Visible)
	// 						{
	// 							Temp->IsHide = false;
	// 							Temp->SetVisibility(ESlateVisibility::Collapsed);
	// 						}
	// 						else if(Temp->GetVisibility() == ESlateVisibility::Collapsed)
	// 						{
	// 							Temp->IsHide = true;
	// 						}
	// 					}
	// 				}
	// 		
	// 				//MySecondRoulette->SetVisibility(ESlateVisibility::Visible);
	// 				SetSkillToSecondRoulette();
	// 				MenuHierarchy = EMenuHierarchy::LevelTwo;
	// 			}
	// 			else if(MyMainRoulette->RouletteState == ERouletteState::Turning)
	// 			{
	// 				MyMainRoulette->ChangeRotateSpeed();
	// 			}
	// 		}
	// 		break;
	// 	}
	// case EMenuHierarchy::LevelTwo:
	// 	{
	// 		if(UGameplayFuncLib::GetAwDataManager()->GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
	// 		GetActionLinkByMainActionId(*MySecondRoulette->GetCurSelectItemInfo().ActionId).Id).CandidateActions.Num() > 0)
	// 		{
	// 			if(MySecondRoulette->RouletteState == ERouletteState::Select)
	// 			{
	// 				MySecondRoulette->SetRouletteState(ERouletteState::Confirm);
	// 				MySecondRoulette->ResetRotateSpeed();
	// 				for (auto Temp : MySecondRoulette->RouletteItemArray)
	// 				{
	// 					if(MySecondRoulette->GetCurSelectItem() == Temp)
	// 					{
	// 						Temp->RouletteItem_Ground->SetVisibility(ESlateVisibility::Collapsed);
	// 						Temp->SetItemSelectVisibility(ESlateVisibility::Collapsed);
	// 						Temp->SetRenderScale(FVector2D(1.0f,1.0f));
	// 					}
	// 					else
	// 					{
	// 						if(Temp->GetVisibility() == ESlateVisibility::Visible)
	// 						{
	// 							Temp->SetVisibility(ESlateVisibility::Collapsed);
	// 						}
	// 						else if(Temp->GetVisibility() == ESlateVisibility::Collapsed)
	// 						{
	// 							Temp->IsHide = true;
	// 						}
	// 					}
	// 				}
	// 		
	// 				MyThirdRoulette->SetVisibility(ESlateVisibility::Visible);
	// 				SetSkillToThirdRoulette();
	// 				MenuHierarchy = EMenuHierarchy::TierThree;
	// 				
	// 			}
	// 			else if(MySecondRoulette->RouletteState == ERouletteState::Turning)
	// 			{
	// 				MySecondRoulette->ChangeRotateSpeed();
	// 			}
	// 			UGameplayFuncLib::GetAwGameState()->MyCharacter->GetActionComponent()->ChangeMainAction(MyMainRoulette->
	// 			GetCurSelectItemInfo().ActionSelection,MySecondRoulette->GetCurSelectItemInfo().ActionId);
	//
	// 			FRouletteItemInfo TempItemInfo;
	// 			TempItemInfo.ActionSelection			= MySecondRoulette->GetCurSelectItemInfo().ActionSelection;
	// 			TempItemInfo.ActionSelectionUIInfo		= MyMainRoulette->GetCurSelectItemInfo().ActionSelectionUIInfo;
	// 			TempItemInfo.IconPath					= MySecondRoulette->GetCurSelectItemInfo().IconPath;
	// 			TempItemInfo.Name						= MySecondRoulette->GetCurSelectItemInfo().Name;
	// 			TempItemInfo.ActionId					= MySecondRoulette->GetCurSelectItemInfo().ActionId;
	// 			MyMainRoulette->SetCurSelectItemInfo(TempItemInfo);
	// 			MyMainRoulette->GetCurSelectItem()->SetRouletteItemAttributes(MyMainRoulette->GetCurSelectItemInfo());
	// 		}
	// 		else
	// 		{
	// 			UGameplayFuncLib::GetAwGameState()->MyCharacter->GetActionComponent()->ChangeMainAction(MyMainRoulette->
	// 			GetCurSelectItemInfo().ActionSelection,MySecondRoulette->GetCurSelectItemInfo().ActionId);
	//
	// 			FRouletteItemInfo TempItemInfo;
	// 			TempItemInfo.ActionSelection			= MySecondRoulette->GetCurSelectItemInfo().ActionSelection;
	// 			TempItemInfo.ActionSelectionUIInfo		= MyMainRoulette->GetCurSelectItemInfo().ActionSelectionUIInfo;
	// 			TempItemInfo.IconPath					= MySecondRoulette->GetCurSelectItemInfo().IconPath;
	// 			TempItemInfo.Name						= MySecondRoulette->GetCurSelectItemInfo().Name;
	// 			TempItemInfo.ActionId					= MySecondRoulette->GetCurSelectItemInfo().ActionId;
	// 			MyMainRoulette->SetCurSelectItemInfo(TempItemInfo);
	// 			MyMainRoulette->GetCurSelectItem()->SetRouletteItemAttributes(MyMainRoulette->GetCurSelectItemInfo());
	// 		}
	// 		
	// 		if(!MySecondRoulette->GetCurSelectItem()->IsEquipped)
	// 		{
	// 			MySecondRoulette->GetCurEquippedItem()->SetRouletteItemIsEquipped(false);
	// 			
	// 			MySecondRoulette->GetCurSelectItem()->SetRouletteItemIsEquipped(true);
	// 		}
	// 		
	// 		break;
	// 	}
	// case EMenuHierarchy::TierThree:
	// 	{
	// 		if(!MyThirdRoulette->GetCurSelectItem()->IsEquipped)
	// 		{
	// 			MyThirdRoulette->GetCurEquippedItem()->SetRouletteItemIsEquipped(false);
	// 			
	// 			MyThirdRoulette->GetCurSelectItem()->SetRouletteItemIsEquipped(true);
	// 			
	// 			UActionComponent* ActionComp = UGameplayFuncLib::GetAwGameState()->MyCharacter->GetActionComponent();
	// 			FActionLink ActionLink = UGameplayFuncLib::GetAwDataManager()->GetActionLinkByMainActionId(*MySecondRoulette->
	// 			GetCurSelectItemInfo().ActionId); 
	// 			
	// 			const FString ChangeToId = MyThirdRoulette->GetCurSelectItemInfo().ActionId;
	// 			FActionInfo* ToAction = ActionComp->GetActionByOriginId(ChangeToId);
	// 			const FString ToId = ToAction ? ToAction->Id : ChangeToId;
	// 			
	// 			ActionComp->ChangeLinkedAction(ActionLink, ToId);
	// 			const FString ChangedId = ActionComp->CurrentLinkedSelectActionId(ActionLink);
	// 		}
	// 		break;
	// 	}
	// }
	// ChangeConfirmName();
}

void UChangeSkill::RouletteStopRotate()
{
	if (MyRoulette[LayerIndex])
		MyRoulette[LayerIndex]->RouletteStopRotate();
	// switch (MenuHierarchy)
	// {
	// case EMenuHierarchy::MainLevel:
	// 	{
	// 		if(MyMainRoulette)
	// 			MyMainRoulette->RouletteStopRotate();
	// 		break;
	// 	}
	// case EMenuHierarchy::LevelTwo:
	// 	{
	// 		if(MySecondRoulette)
	// 			MySecondRoulette->RouletteStopRotate();
	// 		break;
	// 	}
	// case EMenuHierarchy::TierThree:
	// 	{
	// 		if(MyThirdRoulette)
	// 			MyThirdRoulette->RouletteStopRotate();
	// 		break;
	// 	}
	// }
}

void UChangeSkill::RouletteBack()
{
	if (LayerIndex <= 0)
	{
		UGameMain* MainUI = (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain")) ?
				Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]) : nullptr;
		if (MainUI) MainUI->ShowMainUI("InHide");
					
		UGameplayFuncLib::GetUiManager()->Hide("ChangeSkill");
		UGameplayFuncLib::SetAllPlayerGameControlState(EGameControlState::Game);
	}else
	{
		MyRoulette[LayerIndex]->SetIsRouletteFocus(false);
		LayerIndex -= 1;
		MyRoulette[LayerIndex]->SetIsRouletteFocus(true);
	}
	ChangeConfirmName();
	// switch (MenuHierarchy)
	// {
	// case EMenuHierarchy::MainLevel:
	// 	{
	// 		UGameMain* MainUI = (UGameplayFuncLib::GetUiManager()->OpenedWidgets.Contains("GameMain")) ?
	// 			Cast<UGameMain>(UGameplayFuncLib::GetUiManager()->OpenedWidgets["GameMain"]) : nullptr;
	// 		if (MainUI) MainUI->ShowMainUI("InHide");
	// 				
	// 		UGameplayFuncLib::GetUiManager()->Hide("ChangeSkill");
	// 		UGameplayFuncLib::GetMyAwPlayerController()->GameControlState = EGameControlState::Game;
	// 		break;
	// 	}
	// case EMenuHierarchy::LevelTwo:
	// 	{
	// 		if(MySecondRoulette->RouletteState == ERouletteState::Select)
	// 		{
	// 			MySecondRoulette->ResetRotateSpeed();
	// 			MySecondRoulette->ResetRotateAngle();
	// 			MySecondRoulette->SetVisibility(ESlateVisibility::Collapsed);
	// 			for (auto temp : MySecondRoulette->RouletteItemArray)
	// 			{
	// 				temp->RemoveFromParent();
	// 			}
	// 			MySecondRoulette->RouletteItemArray.Empty();
	// 		
	// 			for (auto Temp : MyMainRoulette->RouletteItemArray)
	// 			{
	// 				if(MyMainRoulette->GetCurSelectItem() == Temp)
	// 				{
	// 					Temp->RouletteItem_Ground->SetVisibility(ESlateVisibility::Visible);
	// 					Temp->RouletteItem_IconGround->SetVisibility(ESlateVisibility::Visible);
	// 				}
	// 				else
	// 				{
	// 					if(!Temp->IsHide)
	// 					{
	// 						Temp->SetVisibility(ESlateVisibility::Visible);
	// 					}
	// 					else
	// 					{
	// 						Temp->IsHide = false;
	// 					}
	// 					
	// 				}
	// 			}
	// 			MenuHierarchy = EMenuHierarchy::MainLevel;
	// 			MyMainRoulette->SetRouletteState(ERouletteState::Select);
	// 		}
	// 		else if(MySecondRoulette->RouletteState == ERouletteState::Turning)
	// 		{
	// 			MySecondRoulette->ChangeRotateSpeed();
	// 		}
	// 		break;
	// 	}
	// case EMenuHierarchy::TierThree:
	// 	{
	// 		if(MyThirdRoulette->RouletteState == ERouletteState::Select)
	// 		{
	// 			MyThirdRoulette->ResetRotateSpeed();
	// 			MyThirdRoulette->ResetRotateAngle();
	// 			MyThirdRoulette->SetVisibility(ESlateVisibility::Collapsed);
	// 			for (auto temp : MyThirdRoulette->RouletteItemArray)
	// 			{
	// 				temp->RemoveFromParent();
	// 			}
	// 			MyThirdRoulette->RouletteItemArray.Empty();
	// 		
	// 			for (auto Temp : MySecondRoulette->RouletteItemArray)
	// 			{
	// 				if(MySecondRoulette->GetCurSelectItem() == Temp)
	// 				{
	// 					Temp->RouletteItem_Ground->SetVisibility(ESlateVisibility::Visible);
	// 					Temp->SetItemSelectVisibility(ESlateVisibility::Visible);
	// 					Temp->SetRenderScale(FVector2D(1.5f,1.5f));
	// 				}
	// 				else
	// 				{
	// 					if(!Temp->IsHide)
	// 					{
	// 						Temp->SetVisibility(ESlateVisibility::Visible);
	// 					}
	// 					else
	// 					{
	// 						Temp->IsHide = false;
	// 					}
	// 					
	// 				}
	// 			}
	// 			MenuHierarchy = EMenuHierarchy::LevelTwo;
	// 			MySecondRoulette->SetRouletteState(ERouletteState::Select);
	// 		}
	// 		else if(MyThirdRoulette->RouletteState == ERouletteState::Turning)
	// 		{
	// 			MyThirdRoulette->ChangeRotateSpeed();
	// 		}
	// 		break;
	// 	}
	// }
	// ChangeConfirmName();
}

void UChangeSkill::ChangeConfirmName()
{
	// switch (LayerIndex)
	// {
	// case 0:
	// 	{
	// 		if(MyRoulette[0]->GetCurSelectItemInfo().ActionSelectionUIInfo.Actions.Num() > 0)
	// 		{
	// 			Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
	// 			SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Next")));
	// 		}
	// 		else
	// 		{
	// 			Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
	// 			SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Confirm")));
	// 		}
	// 		break;
	// 	}
	// case 1:
	// 	{
	// 		if(UGameplayFuncLib::GetAwDataManager()->GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
	// 			GetActionLinkByMainActionId(*MyRoulette[1]->GetCurSelectItemInfo().ActionId).Id).CandidateActions.Num() > 0)
	// 		{
	// 			Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
	// 			SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Next")));
	// 		}
	// 		else
	// 		{
	// 			Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
	// 			SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Confirm")));
	// 		}
	// 		break;
	// 	}
	// case 2:
	// 	{
	// 		
	// 		Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
	// 		SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Confirm")));
	// 		
	// 		break;
	// 	}
	// 	default:break;
	// }

	switch (LayerIndex)
	{
	case 0:
		{
			if(UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(
				ActionSelections[MyRoulette[0]->GetCurFocusIndex()].Id).Actions.Num() > 0)
			{
				Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
				SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Next")));
			}
			else
			{
				Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
				SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Confirm")));
			}
			break;
		}
	case 1:
		{
			if(UGameplayFuncLib::GetAwDataManager()->GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
				GetActionLinkByMainActionId(*MyRoulette[1]->GetCurSelectItemInfo().InfoKey).Id).CandidateActions.Num() > 0)
			{
				Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
				SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Next")));
			}
			else
			{
				Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
				SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Confirm")));
			}
			break;
		}
	case 2:
		{
			Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
				SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Confirm")));
			break;
		}
	default:break;
	}
	
	/*Cast<UButtonUI>(ButtonIndication->ButtonIndicationHorizontalBox->GetChildAt(3))->ButtonNameTextBlock->
			SetText(FText::FromString(UGameplayFuncLib::GetDataManager()->GetTextByKey("Confirm")));*/
}

void UChangeSkill::MethodOfRingRotate()
{
	BigShockWaveImage->		SetRenderTransformAngle(BigShockWaveImage->		GetRenderTransformAngle() + BigShockWaveRotateSpeed		* AcceleratedSpeed);
	SmallShockWaveImage->	SetRenderTransformAngle(SmallShockWaveImage->	GetRenderTransformAngle() + SmallShockWaveRotateSpeed	* AcceleratedSpeed);
	BigTriangleImage->		SetRenderTransformAngle(BigTriangleImage->		GetRenderTransformAngle() + BigTriangleRotateSpeed		* AcceleratedSpeed);
	SmallTriangleImage->	SetRenderTransformAngle(SmallTriangleImage->	GetRenderTransformAngle() + SmallTriangleRotateSpeed	* AcceleratedSpeed);
	EightStarImage->		SetRenderTransformAngle(EightStarImage->		GetRenderTransformAngle() + EightStarRotateSpeed		* AcceleratedSpeed);
	CircletImage->			SetRenderTransformAngle(CircletImage->			GetRenderTransformAngle() + CircletRotateSpeed			* AcceleratedSpeed);
	CipherImage->			SetRenderTransformAngle(CipherImage->			GetRenderTransformAngle() + CipherRotateSpeed			* AcceleratedSpeed);
}

void UChangeSkill::MethodOfRingRotateAccelerate()
{
	MethodOfRingState = EMethodOfRingState::IsAccelerating;
	AcceleratedSpeed = 3.0f;

	GetWorld()->GetTimerManager().SetTimer(TimerHandle,this,&UChangeSkill::SetMethodOfRingStateToCollapsing,0.3f);
}

void UChangeSkill::MethodOfRingCollapsing(float InDeltaTime)
{
	FrenchRingBaseCanvasPanel->SetRenderScale(FMath::Vector2DInterpTo(FrenchRingBaseCanvasPanel->GetRenderTransform().Scale,FVector2D(0.0f,0.0f),InDeltaTime,ScaleSpeed));

	//--------------------临时效果 位置暂时写死--------------------
	BigShockWaveImage->		SetRenderTranslation(FMath::Vector2DInterpTo(BigShockWaveImage->
		GetRenderTransform().Translation,FVector2D(-106.0f,-239.0f),InDeltaTime,ScaleSpeed));
	SmallShockWaveImage->	SetRenderTranslation(FMath::Vector2DInterpTo(SmallShockWaveImage->
		GetRenderTransform().Translation,FVector2D(-324.5f,-695.0f),InDeltaTime,ScaleSpeed));
	BigTriangleImage->		SetRenderTranslation(FMath::Vector2DInterpTo(BigTriangleImage->
		GetRenderTransform().Translation,FVector2D(-309.0f,232.0f),InDeltaTime,ScaleSpeed));
	SmallTriangleImage->	SetRenderTranslation(FMath::Vector2DInterpTo(SmallTriangleImage->
		GetRenderTransform().Translation,FVector2D(-156.0f,271.0f),InDeltaTime,ScaleSpeed));
	EightStarImage->		SetRenderTranslation(FMath::Vector2DInterpTo(EightStarImage->
		GetRenderTransform().Translation,FVector2D(-273.5f,741.5f),InDeltaTime,ScaleSpeed));
	CircletImage->			SetRenderTranslation(FMath::Vector2DInterpTo(CircletImage->
		GetRenderTransform().Translation,FVector2D(-523.5f,398.5f),InDeltaTime,ScaleSpeed));
	CipherImage->			SetRenderTranslation(FMath::Vector2DInterpTo(CipherImage->
		GetRenderTransform().Translation,FVector2D(-594.5f,-121.0f),InDeltaTime,ScaleSpeed));

	
	//--------------------临时效果 位置暂时写死--------------------
	
	
	
	if(FrenchRingBaseCanvasPanel->GetRenderTransform().Scale == FVector2D(0.0f,0.0f))
	{
		if(IsConfirm)
		{
			RouletteConfirm();
		}
		else
		{
			RouletteBack();
		}
		
		MethodOfRingState = EMethodOfRingState::Unfolding;
		
	}
}

void UChangeSkill::MethodOfRingUnfold(float InDeltaTime)
{
	BigShockWaveImage->		SetRenderTransformAngle(BigShockWaveImage->		GetRenderTransformAngle() + BigShockWaveRotateSpeed		* AcceleratedSpeed);
	SmallShockWaveImage->	SetRenderTransformAngle(SmallShockWaveImage->	GetRenderTransformAngle() + SmallShockWaveRotateSpeed	* AcceleratedSpeed);
	BigTriangleImage->		SetRenderTransformAngle(BigTriangleImage->		GetRenderTransformAngle() + BigTriangleRotateSpeed		* AcceleratedSpeed);
	SmallTriangleImage->	SetRenderTransformAngle(SmallTriangleImage->	GetRenderTransformAngle() + SmallTriangleRotateSpeed	* AcceleratedSpeed);
	EightStarImage->		SetRenderTransformAngle(EightStarImage->		GetRenderTransformAngle() + EightStarRotateSpeed		* AcceleratedSpeed);
	CircletImage->			SetRenderTransformAngle(CircletImage->			GetRenderTransformAngle() + CircletRotateSpeed			* AcceleratedSpeed);
	CipherImage->			SetRenderTransformAngle(CipherImage->			GetRenderTransformAngle() + CipherRotateSpeed			* AcceleratedSpeed);

	BigShockWaveImage->		SetRenderTranslation(FMath::Vector2DInterpTo(BigShockWaveImage->	GetRenderTransform().Translation,FVector2D(0.0f),InDeltaTime,ScaleSpeed));
	SmallShockWaveImage->	SetRenderTranslation(FMath::Vector2DInterpTo(SmallShockWaveImage->	GetRenderTransform().Translation,FVector2D(0.0f),InDeltaTime,ScaleSpeed));
	BigTriangleImage->		SetRenderTranslation(FMath::Vector2DInterpTo(BigTriangleImage->		GetRenderTransform().Translation,FVector2D(0.0f),InDeltaTime,ScaleSpeed));
	SmallTriangleImage->	SetRenderTranslation(FMath::Vector2DInterpTo(SmallTriangleImage->	GetRenderTransform().Translation,FVector2D(0.0f),InDeltaTime,ScaleSpeed));
	EightStarImage->		SetRenderTranslation(FMath::Vector2DInterpTo(EightStarImage->		GetRenderTransform().Translation,FVector2D(0.0f),InDeltaTime,ScaleSpeed));
	CircletImage->			SetRenderTranslation(FMath::Vector2DInterpTo(CircletImage->			GetRenderTransform().Translation,FVector2D(0.0f),InDeltaTime,ScaleSpeed));
	CipherImage->			SetRenderTranslation(FMath::Vector2DInterpTo(CipherImage->			GetRenderTransform().Translation,FVector2D(0.0f),InDeltaTime,ScaleSpeed));

	
	
	FrenchRingBaseCanvasPanel->SetRenderScale(FMath::Vector2DInterpTo(FrenchRingBaseCanvasPanel->GetRenderTransform().Scale,FVector2D(1.0f),InDeltaTime,ScaleSpeed));
	
	if(FrenchRingBaseCanvasPanel->GetRenderTransform().Scale == FVector2D(1.0f))
	{
		AcceleratedSpeed = 1.0f;

		MethodOfRingState = EMethodOfRingState::None;
	}
}

void UChangeSkill::SetMethodOfRingStateToCollapsing()
{
	MethodOfRingState = EMethodOfRingState::Collapsing;
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle);
}

void UChangeSkill::UpdateSkill()
{
	if(IsValid(MyRoulette[LayerIndex]->GetCurSelectItem()))
	{
		switch (LayerIndex)
		{
		case 0:
			{
				if(MyRoulette[1])
				{
					ChangeSecondSkill(MyRoulette[0]->GetCurFocusIndex());
					if(IsValid(MyRoulette[1]->GetCurSelectItem()))
						MyRoulette[1]->GetCurSelectItem()->SetRenderScale(FVector2D(1));
					MyRoulette[1]->RestRouletteAngleAndRouletteItemAngle();
				}
				if(MyRoulette[2])
				{
					ChangeThirdlySkill(MyRoulette[1]->GetCurFocusIndex());
					if(IsValid(MyRoulette[2]->GetCurSelectItem()))
						MyRoulette[2]->GetCurSelectItem()->SetRenderScale(FVector2D(1));
					MyRoulette[2]->RestRouletteAngleAndRouletteItemAngle();
				}
				
				break;
			}
		case 1:
			{
				if(MyRoulette[2])
				{
					ChangeThirdlySkill(MyRoulette[1]->GetCurFocusIndex());
					if(IsValid(MyRoulette[2]->GetCurSelectItem()))
						MyRoulette[2]->GetCurSelectItem()->SetRenderScale(FVector2D(1));
					MyRoulette[2]->RestRouletteAngleAndRouletteItemAngle();
				}
				break;	
			}
		case 2:
			{
				break;
			}
			default:
				break;
		}
	}
}

void UChangeSkill::ChangeSecondSkill(int Index)
{
	LayerItemInfo[1].RouletteItemInfos.Empty();
	FString SelActionId = ActionSelections.Num() ? LayerItemInfo[0].RouletteItemInfos[Index].InfoKey : "";
	
	TArray<FActionSelectionMainActionUIInfo> ActionSelectionMainActionUIInfo = ActionSelections.Num() ? 
			UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(ActionSelections[Index].Id).Actions :
			TArray<FActionSelectionMainActionUIInfo>();
	
	for (auto temp : MyRoulette[1]->RouletteItemArray)
	{
		temp->RemoveFromParent();
	}
	MyRoulette[1]->RouletteItemArray.Empty();
	

	int m = 0;
	for (auto Temp : ActionSelectionMainActionUIInfo)
	{
		++m;
		FRouletteItemInfo TempItemInfo = FRouletteItemInfo();
		TempItemInfo.InfoKey = Temp.ActionId;
		TempItemInfo.IconPath	= Temp.Icon;
		TempItemInfo.Name	= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.ActionId);
		TempItemInfo.IsShowItemDecorateGround	= false;
		if(Temp.ActionId.IsEmpty() == false && Temp.ActionId == SelActionId)
		{
			TempItemInfo.IsSelected	= true;
			LayerItemInfo[1].RouletteItemInfos.Insert(TempItemInfo,0);
		}
		else
		{
			TempItemInfo.IsSelected	= false;
			LayerItemInfo[1].RouletteItemInfos.Add(TempItemInfo);
		}
	}
	FRouletteInfo Temp = FRouletteInfo();
	int i = 0;
	Temp.Angle = 12.0f;
	Temp.Diameter = 965.0f;
	Temp.PointCount = 7;
	Temp.CompensationValue = 3.0f;
	Temp.ItemPopupDir = EItemPopupDir::Right;
	Temp.RouletteStyle = ERouletteStyle::RouletteLimited;

	MyRoulette[1]->SetRoulette(Temp,this->LayerItemInfo[1].RouletteItemInfos);
	
}

void UChangeSkill::ChangeThirdlySkill(int Index)
{
	LayerItemInfo[2].RouletteItemInfos.Empty();
	for (auto temp : MyRoulette[2]->RouletteItemArray)
	{
		temp->RemoveFromParent();
	}
	MyRoulette[2]->RouletteItemArray.Empty();

	//第三层
	UActionComponent* ActionComp = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActionComponent();
	
	//还在1层，那么第3层显示的是第二层有无后续
	FRouletteItemInfo Layer1Selected = LayerItemInfo[1].RouletteItemInfos[Index];
	//FActionSelectionUIInfo Layer1SelUI = UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(Layer1Selected.InfoKey );
	//已经在2或者3层了
	FString CurrentLinkedSelectActionId = ActionComp->CurrentLinkedSelectActionId(
		UGameplayFuncLib::GetAwDataManager()->GetActionLinkByMainActionId(*Layer1Selected.InfoKey));
	FString OriActionId = ActionComp->GetActionById(CurrentLinkedSelectActionId)->OriginId;
	TArray<FActionLinkCandidateActionUIInfo> ActionLinkCandidateActionUIInfos = UGameplayFuncLib::GetAwDataManager()->
	GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
	GetActionLinkByMainActionId(*Layer1Selected.InfoKey).Id).CandidateActions;

	if(ActionLinkCandidateActionUIInfos.Num() > 0)
	{
		for (FActionLinkCandidateActionUIInfo Temp : ActionLinkCandidateActionUIInfos)
		{
			FRouletteItemInfo TempItemInfo = FRouletteItemInfo();
			TempItemInfo.InfoKey = Temp.ActionId;
			TempItemInfo.IconPath	= Temp.ActionIcon;
			TempItemInfo.Name	= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.ActionId);
			TempItemInfo.IsShowItemDecorateGround	= false;
			if( Temp.ActionId == OriActionId)
			{
				TempItemInfo.IsSelected = true;
				LayerItemInfo[2].RouletteItemInfos.Insert(TempItemInfo,0);
			}
			else
			{
				TempItemInfo.IsSelected = false;
				LayerItemInfo[2].RouletteItemInfos.Add(TempItemInfo);
			}
		}
		FRouletteInfo Temp = FRouletteInfo();
		Temp.Angle = 9.5f;
		Temp.Diameter = 1205.0f;
		Temp.PointCount = 7;
		Temp.CompensationValue = 3.0f;
		Temp.ItemPopupDir = EItemPopupDir::Right;
		Temp.RouletteStyle = ERouletteStyle::RouletteLimited;
		MyRoulette[2]->SetRoulette(Temp,this->LayerItemInfo[2].RouletteItemInfos);
	}
		
}

void UChangeSkill::RefreshLayerItemInfo(TArray<int> Indexes)
{
	this->LayerItemInfo.Empty();
	TMap<int, int> LIndex;	//每一层选中了第几个<层，index>
	for (int i = 0; i < 3; i++)
	{
		this->LayerItemInfo.Add(FLayerRouletteInfo());
		LIndex.Add(i, Indexes.Num() > i ? Indexes[i] : 0);
	}

	//第一层
	for (FActionSelection ASelection : this->ActionSelections)
	{
		FRouletteItemInfo TempItemInfo = FRouletteItemInfo();
		FString CurMainAction = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActionComponent()->CurrentSelectedMainActionId(ASelection);
		
		for (auto Temp : UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(ASelection.Id).Actions)
		{
			if(Temp.ActionId == CurMainAction)
			{
				TempItemInfo.InfoKey = Temp.ActionId;
				TempItemInfo.IconPath	= Temp.Icon;
				TempItemInfo.Name = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.ActionId);
			}
		}
		LayerItemInfo[0].RouletteItemInfos.Add(TempItemInfo);
	}
	MyRoulette[0]->SetIsRouletteFocus(true);
	//第二层
	// FActionSelectionMainActionUIInfo SelInfo = 
	// 	LIndex[0] >= 0 && LIndex[0] < ActionSelections.Num() ?
	// 	UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(ActionSelections[LIndex[0]].Id) : FActionSelectionMainActionUIInfo();
	FString SelActionId = LIndex[0] >= 0 && LIndex[0] < ActionSelections.Num() ? LayerItemInfo[0].RouletteItemInfos[LIndex[0]].InfoKey : "";
	TArray<FActionSelectionMainActionUIInfo> ActionSelectionMainActionUIInfo =
		LIndex[0] >= 0 && LIndex[0] < ActionSelections.Num() ? 
			UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(ActionSelections[LIndex[0]].Id).Actions :
			TArray<FActionSelectionMainActionUIInfo>();

	int m = 0;
	for (auto Temp : ActionSelectionMainActionUIInfo)
	{
		++m;
		FRouletteItemInfo TempItemInfo = FRouletteItemInfo();
		TempItemInfo.InfoKey = Temp.ActionId;
		TempItemInfo.IconPath	= Temp.Icon;
		TempItemInfo.Name	= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.ActionId);
		TempItemInfo.IsShowItemDecorateGround	= false;
		if(Temp.ActionId.IsEmpty() == false && Temp.ActionId == SelActionId)
		{
			TempItemInfo.IsSelected	= true;
			LayerItemInfo[1].RouletteItemInfos.Insert(TempItemInfo,0);
		}
		else
		{
			TempItemInfo.IsSelected	= false;
			LayerItemInfo[1].RouletteItemInfos.Add(TempItemInfo);
		}
	}
	MyRoulette[1]->SetIsRouletteFocus(false);
	//第三层
	UActionComponent* ActionComp = UGameplayFuncLib::GetAwGameState()->GetMyCharacter()->GetActionComponent();
	/*if (LayerIndex < 1)
	{
		//还在1层，那么第3层显示的是第二层有无后续
		
	}else
	{*/
		FRouletteItemInfo Layer1Selected = LayerItemInfo[1].RouletteItemInfos[LIndex[1]];
		//FActionSelectionUIInfo Layer1SelUI = UGameplayFuncLib::GetAwDataManager()->GetActionSelectionUIInfo(Layer1Selected.InfoKey );
		//已经在2或者3层了
		FString CurrentLinkedSelectActionId = ActionComp->CurrentLinkedSelectActionId(
			UGameplayFuncLib::GetAwDataManager()->GetActionLinkByMainActionId(*Layer1Selected.InfoKey));
		FString OriActionId = ActionComp->GetActionById(CurrentLinkedSelectActionId)->OriginId;
		TArray<FActionLinkCandidateActionUIInfo> ActionLinkCandidateActionUIInfos = UGameplayFuncLib::GetAwDataManager()->
		GetActionLinkUIInfo(UGameplayFuncLib::GetAwDataManager()->
		GetActionLinkByMainActionId(*Layer1Selected.InfoKey).Id).CandidateActions;
	
		if(ActionLinkCandidateActionUIInfos.Num() > 0)
		{
			for (FActionLinkCandidateActionUIInfo Temp : ActionLinkCandidateActionUIInfos)
			{
				FRouletteItemInfo TempItemInfo = FRouletteItemInfo();
				TempItemInfo.InfoKey = Temp.ActionId;
				TempItemInfo.IconPath	= Temp.ActionIcon;
				TempItemInfo.Name	= UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Temp.ActionId);
				TempItemInfo.IsShowItemDecorateGround	= false;


				if( Temp.ActionId == OriActionId)
				{
					TempItemInfo.IsSelected = true;
					LayerItemInfo[2].RouletteItemInfos.Insert(TempItemInfo,0);
				}
				else
				{
					TempItemInfo.IsSelected = false;
					LayerItemInfo[2].RouletteItemInfos.Add(TempItemInfo);
				}
			}
		}
	MyRoulette[2]->SetIsRouletteFocus(false);
	//}
}

TArray<int> UChangeSkill::FocusedInRoulette()
{
	TArray<int> Res;
	for (int i = 0; i < MyRoulette.Num(); i++)
	{
		Res.Add(MyRoulette[i]->FocusIndex());
	}
	return Res;
}
// Fill out your copyright notice in the Description page of Project Settings.


#include "MapName.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


void UMapName::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	
	GameInstance = UGameplayFuncLib::GetAwGameInstance();  

	AreaCanvasPanel = Cast<UCanvasPanel>(GetWidgetFromName("Area"));

	AreaNameTextBlock = Cast<UTextBlock>(GetWidgetFromName("AreaName"));
}

void UMapName::NativeConstruct()
{
	Super::NativeConstruct();

	if(GameInstance)
	{
		FString TempName = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(GameInstance->CurMapInfo.MapId);

		AreaNameTextBlock->SetText(FText::FromString(TempName));
	}
	
	PlayWidgetAnim("ShowMapName",0.0f,1,EUMGSequencePlayMode::Forward,1.0f,false);
}

void UMapName::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	if(FMath::IsNearlyEqual(GetAnimationCurrentTime(GetNameWidgetAnimation("ShowMapName")),
		GetNameWidgetAnimation("ShowMapName")->GetEndTime(),0.01f))
	{
		UGameplayFuncLib::GetUiManager()->Hide("MapName");
	}
}

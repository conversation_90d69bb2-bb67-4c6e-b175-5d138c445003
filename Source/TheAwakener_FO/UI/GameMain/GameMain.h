// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CharacterStateBarUI.h"

#include "HitComboWidget.h"
#include "NewThing.h"

//#include "Blueprint/UserWidget.h"
#include "Components/CanvasPanel.h"
#include "Components/Image.h"
#include "Components/ListView.h"
#include "Components/ProgressBar.h"
#include "Components/TextBlock.h"


#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Trading/MerchantShop.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "TheAwakener_FO/UI/Task/TaskUI.h"


#include "GameMain.generated.h"

USTRUCT(BlueprintType)
struct FUIAudio
{
	GENERATED_BODY()

	UPROPERTY()
	FString AudioId = "";

	UPROPERTY()
	FString AudioPath = "";

	FUIAudio(){};

	static FUIAudio FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FUIAudio Res;

		Res.AudioId				= JsonObj->GetStringField("Key");
		Res.AudioPath			= JsonObj->GetStringField("Path");
		return Res;
	}
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UGameMain : public UBaseUI
{
	GENERATED_BODY()
private:
	UPROPERTY()
	AAwCharacter* ShowingCharacter;	
	UPROPERTY()
	FString ShowingIcon = FString();
	UPROPERTY()
	FString MyClassIcon = FString();
	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

	UPROPERTY()
	AAwPlayerController* MyPlayerController;
	
	UPROPERTY()
	float MyCurAnimTime;							//当前UI动画播放到的时间

	// UPROPERTY()
	// UWidgetAnimation* MyCurAnimation;				//当前UI动画

	UPROPERTY()
	bool CanShowUI;									//就两种状态可以显示UI，不能显示UI

	UPROPERTY()
	AMerchantShop* CurShopItem;						//当前选中商品对象

	UPROPERTY()
	UHitComboWidget* HitComboWidget;				//连击数量
	
	
	//还要多久才能再次显示下一个NewThing;
	UPROPERTY()
	float SecToNextNewThing = 0;
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UTextBlock* PlayerNameText;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UProgressBar* HpBar;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UListView* TeamList;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UTextBlock* AreaNameText;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UImage* ClassIconComp;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UCanvasPanel* GotItemArea;
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UImage* UsingItemIcon;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UTextBlock* ItemCount;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<UNewThing*> NewThings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UCharacterStateBarUI* PlayerStateBarUI;

	/*UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UTaskUI* MainTask;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UTaskUI* BranchTask;*/

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool IsMulti = false;
	
	//即将被添加到显示的NewThing
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FThingObj> GoingToShowNewThings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UVerticalBox* MainTaskVerticalBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UVerticalBox* BrabchTaskVerticalBox;

	//凑效果
	UPROPERTY(EditAnywhere,BlueprintReadOnly)
	UTaskUI* ChangeClassOrSkillTask;

	//凑效果
	UPROPERTY(EditAnywhere,BlueprintReadOnly)
	UTaskUI* BlackSmithTask;

	//凑效果
	UPROPERTY()
	FTimerHandle TimerHandle;
	
	
	//FWidgetAnimationDynamicEvent FinishEvent;												//委托事件

	UFUNCTION(BlueprintCallable, BlueprintImplementableEvent)
	void NewThingHint(FThingObj Thing);

	/*UFUNCTION()
	UWidgetAnimation* GetNameWidgetAnimation(const FString& InWidgetAnimName);				//通过输入的UI动画名字查找UI动画

	UFUNCTION()
	void PlayWidgetAnim(const FString& InWidgetAnimName,float StartAtTime,
		int NumLoopsToPlay,EUMGSequencePlayMode::Type PlayModeType,float PlayBackSpeed,bool RestoreState);		//播放UI动画*/

	UFUNCTION(BlueprintCallable)
	void ShowMainUI(const FString& InWidgetAnimName);										//显示UI

	UFUNCTION(BlueprintCallable)
	void HiddeMainUI(const FString& InWidgetAnimName);										//隐藏UI
	
	
	UFUNCTION()
	void Rest();																			//重置
	
	UFUNCTION()
	void AnimIsComplete();																	//UI动画播放完成
	
	FORCEINLINE void SetCurItem(AMerchantShop* CurSelected){CurShopItem = CurSelected;}		//设置当前选中商品

	//使用选中的道具
	UFUNCTION()
	void StartUseSelectedItem(EItemUseMethod UseMethod = EItemUseMethod::Use) const;
	//上一个选中道具
	UFUNCTION()
	void SelectPrevItem() const;
	//下一个选中道具
	UFUNCTION()
	void SelectNextItem() const;

	/**
	 * 显示连击数量
	 * @param Combo 连了多少下
	 * @param EndInSec 多少秒结束
	 */
	void ShowHitCombo(int Combo, float EndInSec) const;

	UFUNCTION()
	void SetAreaNameText();

	UFUNCTION(BlueprintImplementableEvent)
	void RefreshUI();

	UFUNCTION(BlueprintCallable)
	void PlayMainTaskAnim();

	UFUNCTION(BlueprintCallable)
	void PlayBranchTaskAnim();

	//凑效果
	UFUNCTION()
	void TempTaskMove();

	//凑效果
	UFUNCTION()
	void UpdateTaskPos();


	UFUNCTION(BlueprintCallable)
	void PlayPlayerStateBarManaNUllFX();
};



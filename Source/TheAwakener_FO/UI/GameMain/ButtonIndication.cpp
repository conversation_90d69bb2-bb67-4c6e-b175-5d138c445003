// Fill out your copyright notice in the Description page of Project Settings.


#include "ButtonIndication.h"

void UButtonIndication::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	ButtonIndicationGroundImage			= Cast<UImage>(GetWidgetFromName("ButtonIndicationGround"));
	ButtonIndicationHorizontalBox		= Cast<UHorizontalBox>(GetWidgetFromName("ButtonIndicationHorizontal"));
	
}

void UButtonIndication::NativeConstruct()
{
	Super::NativeConstruct();
}

void UButtonIndication::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

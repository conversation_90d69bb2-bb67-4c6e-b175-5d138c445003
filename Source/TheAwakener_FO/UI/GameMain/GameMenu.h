// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MenuList.h"
#include "PlayerInformationUI.h"

#include "Roulette.h"

#include "Components/Image.h"
#include "Components/Overlay.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "TheAwakener_FO/UI/MapUI/BigMap.h"

#include "GameMenu.generated.h"

/**
 * 
 */

USTRUCT(BlueprintType)
struct FClassIcon
{
	GENERATED_BODY()

	UPROPERTY(VisibleAnywhere,BlueprintReadWrite)
	FString Id = "";

	UPROPERTY(VisibleAnywhere,BlueprintReadWrite)
	FString IconPath = "";


	FClassIcon(){};

	static FClassIcon FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FClassIcon Res;

		Res.Id				= JsonObj->GetStringField("Id");
		Res.IconPath		= JsonObj->GetStringField("Path");
		return Res;
	}

	
};

UENUM(BlueprintType)
enum class EMenuMember : uint8
{
	Equipment,
	ItemShortcutBar,
	PowerMap,
	SystemSettings,
	BackGameTitle,
	NewbieRetrospect,
	Map
};

UCLASS()
class THEAWAKENER_FO_API UGameMenu : public UBaseUI
{
	GENERATED_BODY()

	
	//游戏状态
	UPROPERTY()
	AAwGameState* GameState;
	
	//轮盘
	UPROPERTY()
	URoulette* Roulette;

	//列表
	UPROPERTY()
	UMenuList* MenuList;

	UPROPERTY()
	EMenuMember MenuMember;

	FTimerHandle TimerHandle;
	
	FTimerHandle TimerHandle1;
	
	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	

public:
	
	
	//菜单背景
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* Menu_BlackGround;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UPlayerInformationUI* PlayerInformationUI;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UBigMap* BigMap;
	
	//物品点数组
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<UOverlay*> PointArray;
	

	UFUNCTION(BlueprintCallable)
	URoulette* GetRoulette(){return Roulette;}

	UFUNCTION(BlueprintCallable)
	UMenuList* GetMenuList(){return MenuList;}

	UFUNCTION(BlueprintCallable)
	void OpenChildUI();

	UFUNCTION(BlueprintCallable)
	void SetListInitScroll(int Index);

	UFUNCTION()
	void HideMenu();

	UFUNCTION()
	void UpdateAnimPlay();

	UFUNCTION()
	void SetUIControlState();

	UFUNCTION(BlueprintImplementableEvent)
	void BackGameTitle();

	UFUNCTION()
	void InitMenuList();
};



// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneCaptureComponent2D.h"
#include "GameFramework/Actor.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Characters/AvatarCharacter/AvatarCharacter.h"
#include "ModelMappingToUI.generated.h"

UCLASS()
class THEAWAKENER_FO_API AModelMappingToUI : public AActor
{
	GENERATED_BODY()

	AAwCharacter* Me = nullptr;
	
public:	
	// Sets default values for this actor's properties
	AModelMappingToUI();
	
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	USceneCaptureComponent2D* SceneCaptureCamera;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	USceneComponent* RotatePoint;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FRotator RotatePointRotator;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UChildActorComponent* EquipmentWeaponComponent;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UChildActorComponent* CharacterCoatHanger;

	TArray<FEquipmentAppearancePart> TestEquipments;
	
	bool EquipmentIsWeapon = false;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	float RotateSpeed = 1.0f;
	
private:
	UPROPERTY()
	AAvatarCharacter* PreviewCharacter;

	UPROPERTY()
	TArray<AActor*> PreviewEquips;

	UPROPERTY()
	FVector SceneCaptureCameraLocation = FVector::ZeroVector;

	UPROPERTY()
	FRotator ModelRotator = FRotator(0);

	UPROPERTY()
	FVector ModelLocation = FVector::ZeroVector;

	UPROPERTY()
	FTimerHandle TimerHandle;
protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

public:	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	
	UFUNCTION(BlueprintCallable)
	void SetRotatePointRotator(FRotator Rotator){ RotatePointRotator = Rotator;}

	UFUNCTION(BlueprintCallable)
	void SetRotatePointTransform(FTransform Transform){RotatePoint->SetRelativeTransform(Transform);}

	UFUNCTION(BlueprintCallable)
	void SetSceneCaptureCameraFOVAngle(float FOVAngle){SceneCaptureCamera->FOVAngle = FOVAngle;}
	
	UFUNCTION(BlueprintCallable)
	void ChangeEquipmentModel(TArray<FEquipmentAppearancePart> Equipments, bool IsWeapon);

	UFUNCTION(BlueprintCallable)
	void SpawnEquipmentModel();

	UFUNCTION(BlueprintCallable)
	void DestroyEquipmentModel();
	
	UFUNCTION(BlueprintCallable)
	void SetSceneCaptureCameraLocation(FVector Location){ SceneCaptureCameraLocation = Location;}
	
	UFUNCTION()
	void SetModelRotator(FRotator Rotator){ ModelRotator = Rotator;}

	UFUNCTION(BlueprintCallable)
	void SetModelLocation(FVector Location){ ModelLocation = Location;}
};

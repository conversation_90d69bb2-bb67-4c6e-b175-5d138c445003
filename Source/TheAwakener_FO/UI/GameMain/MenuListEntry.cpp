// Fill out your copyright notice in the Description page of Project Settings.


#include "MenuListEntry.h"
#include "Blueprint/IUserObjectListEntry.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/CanvasPanelSlot.h"
#include "Components/HorizontalBoxSlot.h"
#include "Engine/Canvas.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"

void UMenuListEntry::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	MenuList_EntryBase			= Cast<UCanvasPanel>(GetWidgetFromName(TEXT("EntryBase")));

	Entry_Ground				= Cast<UOverlay>(GetWidgetFromName(TEXT("EntryGroundBase")));
	
	Icon_GroundBase				= Cast<UOverlay>(GetWidgetFromName(TEXT("IconGroundBase")));
	
	Icon_BackGround				= Cast<UImage>(GetWidgetFromName(TEXT("IconGround")));

	IconImage					= Cast<UImage>(GetWidgetFromName(TEXT("Icon")));

	EntryCanvasPanel			= Cast<UCanvasPanel>(GetWidgetFromName(TEXT("EntryCanvas")));
	
	EntryBackGround				= Cast<UImage>(GetWidgetFromName(TEXT("EntryGround")));

	EntryName					= Cast<UTextBlock>(GetWidgetFromName(TEXT("Name")));

	EntryTypeName				= Cast<UTextBlock>(GetWidgetFromName(TEXT("EntryType")));

	EntryMakerBox				= Cast<UImage>(GetWidgetFromName(TEXT("MakerBox")));

	SelectBox					= Cast<UImage>(GetWidgetFromName(TEXT("IconSelectBox")));
	
	ItemIconOverlay				= Cast<UOverlay>(GetWidgetFromName(TEXT("ItemIconBase")));

	ItemIconImage				= Cast<UImage>(GetWidgetFromName(TEXT("ItemIcon")));

	MakeIconOverlay				= Cast<UOverlay>(GetWidgetFromName(TEXT("MakeIconBase")));

	MakeIconGroundImage			= Cast<UImage>(GetWidgetFromName(TEXT("MakeIconGround")));

	EntryShadeImage				= Cast<UImage>(GetWidgetFromName(TEXT("EntryShade")));
	
	TweenFunc.ClassPath = "UIList";
	TweenFunc.FunctionName = "CalculateListPosition";
	Func = UCallFuncLib::GetUFunction(TweenFunc.ClassPath, TweenFunc.FunctionName);
	if(IsValid(ItemIconOverlay))
		ItemIconOverlay->SetVisibility(ESlateVisibility::Collapsed);
	if(IsValid(MakeIconGroundImage))
		MakeIconGroundImage->SetVisibility(ESlateVisibility::Collapsed);
	if(IsValid(EntryShadeImage))
		EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
}

void UMenuListEntry::NativeConstruct()
{
	Super::NativeConstruct();

	FSlateFontInfo Temp;
	Temp.FontObject = EntryName->GetFont().FontObject;
	Temp.FontMaterial = EntryName->GetFont().FontMaterial;
	Temp.OutlineSettings = EntryName->GetFont().OutlineSettings;
	Temp.TypefaceFontName = EntryName->GetFont().TypefaceFontName;
	Temp.Size = 16;
	Temp.LetterSpacing = 300;

	EntryName->SetFont(Temp);
	
}

void UMenuListEntry::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	
	if(Func)
	{
		struct
		{
			FGeometry MyGeometry;
			float interval;
			float curveAngle;
			float displacementValue;
			float compensationValue;


			FVector2D Result;
		}TweenFuncParam;
		
		if(EntryData)
		{
			TweenFuncParam.MyGeometry = MyGeometry;
			TweenFuncParam.interval = EntryData->Interval;
			TweenFuncParam.curveAngle = EntryData->CurveAngle;
			TweenFuncParam.displacementValue = EntryData->Diameter;
			TweenFuncParam.compensationValue = EntryData->CompensationValue;

			this->ProcessEvent(Func, &TweenFuncParam);
		}
		
		UWidgetLayoutLibrary::SlotAsCanvasSlot(MenuList_EntryBase)->SetPosition(FVector2D(TweenFuncParam.Result.X,
			UWidgetLayoutLibrary::SlotAsCanvasSlot(MenuList_EntryBase)->GetPosition().Y));
		
	}
	
}


void UMenuListEntry::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	IUserObjectListEntry::NativeOnListItemObjectSet(ListItemObject);

	EntryData = Cast<UMenuListEntryData>(ListItemObject);
	
	SetEntryData();
	
	ListItemObjectSetDelegate.Broadcast();
}

void UMenuListEntry::NativeOnItemSelectionChanged(bool bIsSelected)
{
	IUserObjectListEntry::NativeOnItemSelectionChanged(bIsSelected);

	if(bIsSelected)
	{
		EntryData->ElementState = EElementState::State_Select;
	}
	else
	{
		EntryData->ElementState = EElementState::State_Generally;
	}
	
	UpdateView();
	
}


void UMenuListEntry::SetEntryData()
{
	if(EntryData->IconGroundImage)
	{
		Icon_BackGround->SetBrushFromTexture(EntryData->IconGroundImage);
		Icon_BackGround->SetDesiredSizeOverride(FVector2D(EntryData->IconGroundImage->GetSizeX(),EntryData->IconGroundImage->GetSizeY()));
	}
	else
	{
		Icon_BackGround->SetVisibility(ESlateVisibility::Collapsed);
	}
		
	if(EntryData->IconImage)
	{
		IconImage->SetBrushFromTexture(EntryData->IconImage);
		IconImage->SetDesiredSizeOverride(FVector2D(EntryData->IconImage->GetSizeX(),EntryData->IconImage->GetSizeY()));
		ItemIconImage->SetBrushFromTexture(EntryData->IconImage);
		ItemIconImage->SetDesiredSizeOverride(FVector2D(EntryData->IconImage->GetSizeX() / 2,EntryData->IconImage->GetSizeY() / 2));
	}
		
	if(EntryData->EntryGroundImage)
	{
		EntryBackGround->SetBrushFromTexture(EntryData->EntryGroundImage);
	}
		
	if(EntryData->EntrySelectBoxImage)
	{
		SelectBox->SetBrushFromTexture(EntryData->EntrySelectBoxImage);
	}
	else
	{
		SelectBox->SetVisibility(ESlateVisibility::Collapsed);
	}

	if(EntryData->EntryMakerBoxImage)
	{
		EntryMakerBox->SetBrushFromTexture(EntryData->EntryMakerBoxImage);
		EntryMakerBox->SetDesiredSizeOverride(FVector2D(EntryData->EntryMakerBoxImage->GetSizeX(),EntryData->EntryMakerBoxImage->GetSizeY()));
	}
	
	if(EntryData->ItemType == "Item")
	{
		//ItemIconOverlay->SetVisibility(ESlateVisibility::Visible);
		MakeIconGroundImage->SetVisibility(ESlateVisibility::Visible);
	}
	
	
	EntryName->SetText(FText::FromString(EntryData->Name));
	EntryTypeName->SetText(FText::FromString(EntryData->TypeName));
	
	SetEntryStyle(EntryData->EntryStyle);
	UpdateView();
}


void UMenuListEntry::UpdateView()
{

	EntryName->SetRenderScale(FVector2D(1));
	FSlateFontInfo Temp;

	if (!EntryData)return;
	
 	if(EntryData->EntryStyle != EEntryStyle::EntryCentered)
	{
 		if(EntryData->IsShrink)
 		{
	        switch (EntryData->ElementState)
	        {
	        case EElementState::State_Generally:

	        	Entry_Ground->SetRenderOpacity(0.0f);
	        	
	        	SelectBox->SetOpacity(0.0f);
	        	Temp.FontObject = EntryName->GetFont().FontObject;
	        	Temp.FontMaterial = EntryName->GetFont().FontMaterial;
	        	Temp.OutlineSettings = EntryName->GetFont().OutlineSettings;
	        	Temp.TypefaceFontName = EntryName->GetFont().TypefaceFontName;
	        	Temp.Size = 20;
	        	Temp.LetterSpacing = -75;

	        	EntryName->SetFont(Temp);
	        	
	        	//UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(0.0f,0.0f,10.0f,0.0f));
		
	        	//EntryName->SetColorAndOpacity(FSlateColor(FLinearColor(0.0f,0.0f,0.0f,1.0f)));

	        	if(EntryData->EntryGroundSelectBoxImage)
	        	{
	        		EntryBackGround->SetBrushFromTexture(EntryData->EntryGroundSelectBoxImage);
	        		EntryBackGround->SetDesiredSizeOverride(FVector2D(EntryData->EntryGroundSelectBoxImage->GetSizeX(),EntryData->EntryGroundSelectBoxImage->GetSizeY()));
	        	}
	        	break;
	        case EElementState::State_Select:

	        	Entry_Ground->SetRenderOpacity(1.0f);
	        	
	        	SelectBox->SetOpacity(1.0f);
		
	        	Temp.FontObject = EntryName->GetFont().FontObject;
	        	Temp.FontMaterial = EntryName->GetFont().FontMaterial;
	        	Temp.OutlineSettings = EntryName->GetFont().OutlineSettings;
	        	Temp.TypefaceFontName = EntryName->GetFont().TypefaceFontName;
	        	Temp.Size = 20;
	        	Temp.LetterSpacing = -75;

	        	EntryName->SetFont(Temp);
	        	//UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(0.0f,0.0f,10.0f,0.0f));
		
	        	//EntryName->SetColorAndOpacity(FSlateColor(FLinearColor(0.0f,0.0f,0.0f,1.0f)));

	        	if(EntryData->EntryGroundSelectBoxImage)
	        	{
	        		EntryBackGround->SetBrushFromTexture(EntryData->EntryGroundSelectBoxImage);
	        		EntryBackGround->SetDesiredSizeOverride(FVector2D(EntryData->EntryGroundSelectBoxImage->GetSizeX(),EntryData->EntryGroundSelectBoxImage->GetSizeY()));
	        	}
	        	
	        	break;
	        }
 		}
        else
        {
        	switch (EntryData->ElementState)
        	{
        	case EElementState::State_Generally:
        		SelectBox->SetOpacity(0.0f);
		
        		Temp.FontObject = EntryName->GetFont().FontObject;
        		Temp.FontMaterial = EntryName->GetFont().FontMaterial;
        		Temp.OutlineSettings = EntryName->GetFont().OutlineSettings;
        		Temp.TypefaceFontName = EntryName->GetFont().TypefaceFontName;
        		Temp.Size = 14;
        		Temp.LetterSpacing = -75;

        		EntryName->SetFont(Temp);
        		//UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(0.0f,0.0f,30.0f,0.0f));
		
        		//EntryName->SetColorAndOpacity(FSlateColor(FLinearColor(1.0f,0.9f,0.5f,1.0f)));
			
        		if(EntryData->EntryGroundImage)
        		{
        			EntryBackGround->SetBrushFromTexture(EntryData->EntryGroundImage);
        			EntryBackGround->SetDesiredSizeOverride(FVector2D(EntryData->EntryGroundImage->GetSizeX(),EntryData->EntryGroundImage->GetSizeY()));
        		}
			
		
        		break;
        	case EElementState::State_Select:

        		
        		
        		SelectBox->SetOpacity(1.0f);
		
        		Temp.FontObject = EntryName->GetFont().FontObject;
        		Temp.FontMaterial = EntryName->GetFont().FontMaterial;
        		Temp.OutlineSettings = EntryName->GetFont().OutlineSettings;
        		Temp.TypefaceFontName = EntryName->GetFont().TypefaceFontName;
        		Temp.Size = 20;
        		Temp.LetterSpacing = -75;

        		EntryName->SetFont(Temp);
        		//UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(0.0f,0.0f,10.0f,0.0f));
		
        		//EntryName->SetColorAndOpacity(FSlateColor(FLinearColor(0.0f,0.0f,0.0f,1.0f)));
			
        		if(EntryData->EntryGroundSelectBoxImage)
        		{
        			EntryBackGround->SetBrushFromTexture(EntryData->EntryGroundSelectBoxImage);
        			EntryBackGround->SetDesiredSizeOverride(FVector2D(EntryData->EntryGroundSelectBoxImage->GetSizeX(),EntryData->EntryGroundSelectBoxImage->GetSizeY()));
        		}
        		break;
        	}
        }
		
	}
	else
	{
		switch (EntryData->ElementState)
		{
		case EElementState::State_Generally:
		
		
			SelectBox->SetOpacity(0.0f);
			
			Temp.FontObject = EntryName->GetFont().FontObject;
			Temp.FontMaterial = EntryName->GetFont().FontMaterial;
			Temp.OutlineSettings = EntryName->GetFont().OutlineSettings;
			Temp.TypefaceFontName = EntryName->GetFont().TypefaceFontName;
			Temp.Size = 14;
			Temp.LetterSpacing = -75;

			EntryName->SetFont(Temp);
			
			//UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(0.0f,0.0f,0.0f,0.0f));
		
			//EntryName->SetColorAndOpacity(FSlateColor(FLinearColor(1.0f,0.9f,0.5f,1.0f)));


			EntryBackGround->SetOpacity(0.0f);
			
			break;
		case EElementState::State_Select:
		
			SelectBox->SetOpacity(1.0f);
			
			Temp.FontObject = EntryName->GetFont().FontObject;
			Temp.FontMaterial = EntryName->GetFont().FontMaterial;
			Temp.OutlineSettings = EntryName->GetFont().OutlineSettings;
			Temp.TypefaceFontName = EntryName->GetFont().TypefaceFontName;
			Temp.Size = 20;
			Temp.LetterSpacing = -75;

			EntryName->SetFont(Temp);
			
			//UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(0.0f,0.0f,0.0f,0.0f));
		
			//EntryName->SetColorAndOpacity(FSlateColor(FLinearColor(0.0f,0.0f,0.0f,1.0f)));

			EntryBackGround->SetOpacity(1.0f);

			if(EntryData->EntryGroundSelectBoxImage)
			{
				EntryBackGround->SetBrushFromTexture(EntryData->EntryGroundSelectBoxImage);
				EntryBackGround->SetDesiredSizeOverride(FVector2D(EntryData->EntryGroundSelectBoxImage->GetSizeX(),EntryData->EntryGroundSelectBoxImage->GetSizeY()));
			}
			
			break;
		}
	}
	
	if(EntryData->IsEquipped)
	{
		MakeIconOverlay->SetVisibility(ESlateVisibility::Visible);
		EntryMakerBox->SetVisibility(ESlateVisibility::Visible);
		//EntryTypeName->SetVisibility(ESlateVisibility::Visible);
		/*EntryBackGround->SetVisibility(ESlateVisibility::Visible);
		EntryShadeImage->SetVisibility(ESlateVisibility::Visible);*/
	}
	else
	{
		MakeIconOverlay->SetVisibility(ESlateVisibility::Collapsed);
		EntryMakerBox->SetVisibility(ESlateVisibility::Collapsed);
		//EntryTypeName->SetVisibility(ESlateVisibility::Collapsed);
		/*EntryBackGround->SetVisibility(ESlateVisibility::Collapsed);
		EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);*/
	}
	
}

void UMenuListEntry::SetEntryStyle(EEntryStyle EntryStyle)
{
	switch (EntryStyle)
	{
	case EEntryStyle::EntryRight:

		UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->SetAnchors(FAnchors(0.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->SetAlignment(FVector2D(0.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->SetPosition(FVector2D(0.0f,0.0f));
		

		EntryBackGround->SetRenderScale(FVector2D(1.0f,1.0f));

		UWidgetLayoutLibrary::SlotAsCanvasSlot(EntryCanvasPanel)->SetAnchors(FAnchors(0.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(EntryCanvasPanel)->SetAlignment(FVector2D(0.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(EntryCanvasPanel)->SetPosition(
			FVector2D(UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->GetSize().X / 2,0.0f));

		/*UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetHorizontalAlignment(HAlign_Left);
		UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetVerticalAlignment(VAlign_Center);
		UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(160.0f,0.0f,0.0f,0.0f));*/
		
		break;
	case EEntryStyle::EntryCentered:
		
		Icon_GroundBase->SetVisibility(ESlateVisibility::Collapsed);
		Icon_GroundBase->SetRenderScale(FVector2D(0));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(EntryCanvasPanel)->SetPosition(FVector2D(75.0f,0));
		
		break;
	case EEntryStyle::EntryLeft:
		
		UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->SetAnchors(FAnchors(1.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->SetAlignment(FVector2D(1.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->SetPosition(FVector2D(0.0f,0.0f));
		

		EntryBackGround->SetRenderScale(FVector2D(1.0f,1.0f));
		
		
		UWidgetLayoutLibrary::SlotAsCanvasSlot(EntryCanvasPanel)->SetAnchors(FAnchors(1.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(EntryCanvasPanel)->SetAlignment(FVector2D(1.0f,0.5f));
		UWidgetLayoutLibrary::SlotAsCanvasSlot(EntryCanvasPanel)->SetPosition(
			FVector2D(-UWidgetLayoutLibrary::SlotAsCanvasSlot(Icon_GroundBase)->GetPosition().X / 2 - EntryData->Diameter,0.0f));
		
		/*UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetHorizontalAlignment(HAlign_Right);
		UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetVerticalAlignment(VAlign_Center);
		UWidgetLayoutLibrary::SlotAsOverlaySlot(EntryName)->SetPadding(FMargin(0.0f,0.0f,160.0f,0.0f));*/
		break;
	}
}

void UMenuListEntry::PlayEntryAnim()
{
	switch (EntryData->EntryStyle)
	{
	case EEntryStyle::EntryLeft:
		
		PlayWidgetAnim(TEXT("EntryMoveRight"),MyCurAnimTime,1,EUMGSequencePlayMode::Forward,1.0f,false);
		
		break;
	case EEntryStyle::EntryRight:
		
		PlayWidgetAnim(TEXT("EntryMoveLeft"),MyCurAnimTime,1,EUMGSequencePlayMode::Forward,1.0f,false);
		
		break;
		
	default:
		break;
	}
}

void UMenuListEntry::SetEntryMakerBox(FString IconPath)
{
	UTexture2D* Icon = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(IconPath));
	if(Icon)
		EntryMakerBox->SetBrushFromTexture(Icon);
}


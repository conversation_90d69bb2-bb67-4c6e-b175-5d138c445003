// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "MenuListEntry.h"
#include "Blueprint/UserWidget.h"
#include "Components/Image.h"
#include "TheAwakener_FO/UI/Base/AwListView.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "MenuList.generated.h"

/**
 * 
 */


//列表风格
UENUM(BlueprintType)
enum class EListStyle:uint8
{
	//无限列表，即选到最后一个元素再往下选则会选择到第一个元素，反之同理
	ListEndless,
	//有限列表，即选到最后一个元素再往下选不会继续移动选择
	ListLimited
};

//列表状态
UENUM()
enum ListState
{
	//列表选择状态（基本状态）
	ListSelect,
	//列表选择状态改变状态
	ListSelectChanging
};

USTRUCT(BlueprintType)
struct FItemIcon
{
	GENERATED_BODY()

	UPROPERTY(VisibleAnywhere,BlueprintReadWrite)
	FString Id = "";

	UPROPERTY(VisibleAnywhere,BlueprintReadWrite)
	FString Path = "";

	static FItemIcon FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FItemIcon Res	= FItemIcon();
		Res.Id			= JsonObj->GetStringField("Id");
		Res.Path		= JsonObj->GetStringField("Path");
		return  Res;
	}
};


USTRUCT(BlueprintType)
struct FListItems
{
	GENERATED_BODY()

	UPROPERTY()
	FString UniqueId;

	int Index = 0;

	int Count = 0;

	bool IsMainHand = false;

	float EquipmentAttributValue = 0.0f;

	FVector SceneCameraLocation = FVector(0.0f,0.0f,0.0f);

	FRotator ModelRotator = FRotator(0.0f,0.0f,0.0f);

	FVector ModelLocation = FVector(0.0f,0.0f,0.0f);
	
	//对应json中 UI名字（只需要打开UI使用）
	FString Id = "";

	//图标底版图片
	UPROPERTY()
	FString IconGroundImage = "";
	//图标图片
	UPROPERTY()
	FString IconImage = "";
	
	//选项名字
	UPROPERTY()
	FString Name = "";
	//标记图片
	UPROPERTY()
	FString MakerImage = "";
	//选择图片
	UPROPERTY()
	FString SelectImage = "";

	UPROPERTY()
	FString EntryGroundImage = "";

	UPROPERTY()
	FString EntrySelectImage = "";

	UPROPERTY()
	FString ListItemType = "";

	UPROPERTY()
	FString ItemId = "";

	UPROPERTY()
	FString TypeName = "";

	UPROPERTY()
	TArray<FString> Affix;
	

	//列表内容是否被标记
	UPROPERTY()
	bool IsEquipped = false;
	
	
	//列表内容状态
	EElementState ElementState = EElementState::State_Generally;

	//列表内容排列方式
	EEntryStyle EntryStyle = EEntryStyle::EntryLeft;

	static FListItems FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FListItems Res;
		Res.Id					= 	JsonObj->GetStringField("ListItemId");
		Res.ListItemType		=	JsonObj->GetStringField("ListItemType");
		Res.IconGroundImage		= 	JsonObj->GetStringField("IconGroundImage");
		Res.IconImage			= 	JsonObj->GetStringField("IconImage");
		Res.Name				=	JsonObj->GetStringField("Name");
		Res.TypeName			= 	JsonObj->GetStringField("TypeName");
		Res.MakerImage			= 	JsonObj->GetStringField("MakerImage");
		Res.SelectImage			= 	JsonObj->GetStringField("SelectImage");
		Res.IsEquipped			= 	JsonObj->GetBoolField("IsEquipped");
		Res.EntryGroundImage	=	JsonObj->GetStringField("EntryGroundImage");
		Res.EntrySelectImage    =	JsonObj->GetStringField("EntrySelectImage");
		Res.ElementState		= 	UDataFuncLib::FStringToEnum<EElementState>(JsonObj->GetStringField("EElementState"));
		Res.EntryStyle			= 	UDataFuncLib::FStringToEnum<EEntryStyle>(JsonObj->GetStringField("EEntryStyle"));
		Res.SceneCameraLocation.InitFromString(JsonObj->GetStringField("SceneCameraLocation"));
		return Res;
	}
};

USTRUCT(BlueprintType)
struct FListItemElementInfo
{
	GENERATED_BODY()

	UPROPERTY()
	EListStyle ListStyle = EListStyle::ListLimited;

	UPROPERTY()
	float EntrySpace = 0.0f;

	UPROPERTY()
	FString ListBlackGroundImage = "";

	UPROPERTY()
	FVector2D ListGroundPosition = FVector2D(0,0);

	UPROPERTY()
	FVector2D ListPosition = FVector2D(0,0);

	UPROPERTY()
	FVector2D ListMaxSize = FVector2D(0,0);

	UPROPERTY()
	float ListComValue = 0.0f;

	bool IsShrink = false;
	
	//
	FString ListId = "";
	
	//列表排列曲线间隔值
	UPROPERTY()
	float InterValue = 0.0f;
	//列表排列曲线角度 即 90°为半个正弦函数曲线 180°为整个 360°为两个
	UPROPERTY()
	float CurveAngle = 0.0f;
	//列表里内容子控件位移值
	UPROPERTY()
	float DisplacementValue = 0.0f;
	//列表里内容子控件位移补偿值
	UPROPERTY()
	float CompensationValue = 0.0f; 
	

	TArray<FListItems> Items;

	FListItemElementInfo(){};


	static FListItemElementInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FListItemElementInfo Res;
		
		Res.ListId					= 	JsonObj->GetStringField("ListId");
		Res.IsShrink				=	JsonObj->GetBoolField("IsShrink");
			
		Res.InterValue 				= 	JsonObj->GetNumberField("InterValue");
		Res.CurveAngle 				= 	JsonObj->GetNumberField("CurveAngle");
		Res.DisplacementValue 		= 	JsonObj->GetNumberField("DisplacementValue");
		Res.CompensationValue 		= 	JsonObj->GetNumberField("CompensationValue");
	
		Res.ListStyle				=	UDataFuncLib::FStringToEnum<EListStyle>(JsonObj->GetStringField("ListStyle"));
		Res.EntrySpace				=	JsonObj->GetNumberField("EntrySpace");
		Res.ListBlackGroundImage	=	JsonObj->GetStringField("ListBlackGroundImage");
		Res.ListComValue			=	JsonObj->GetNumberField("ListComValue");
		Res.ListPosition.InitFromString(JsonObj->GetStringField("ListPosition"));
		Res.ListGroundPosition.InitFromString(JsonObj->GetStringField("ListGroundPosition"));
		Res.ListMaxSize.InitFromString(JsonObj->GetStringField("ListMaxSize"));
		
		for(const TSharedPtr<FJsonValue> JsonValue : UDataFuncLib::AwGetArrayField(JsonObj,"ListItems"))
		{
			FListItems Item = FListItems::FromJson(JsonValue->AsObject());
			Res.Items.Add(Item);
		}
		
		return Res;
	}
};



UCLASS()
class THEAWAKENER_FO_API UMenuList : public UBaseUI
{
	GENERATED_BODY()

	//选择地子控件
	UPROPERTY()
	UMenuListEntryData* SelectData;

	//选择的索引值
	UPROPERTY()
	int SelectIndex;

	//列表大小补偿值
	UPROPERTY()
	float ListComValue;

	
	/*EKeys UpKey;
	
	EKeys DownKey;*/

	//时间句柄(用作延迟用)
	FTimerHandle TimeHandle;
	
	
	/*virtual FReply NativeOnKeyUp(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;
	virtual FReply NativeOnKeyDown(const FGeometry& InGeometry, const FKeyEvent& InKeyEvent) override;*/
	virtual void NativeOnInitialized() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;
	virtual void RemoveFromParent() override;
	
	
public:

	//列表最大尺寸
	FVector2D ListSizeMax;

	//列表状态
	ListState ListState;
	
	//列表风格
	EListStyle ListStyle;

	bool CurSelectIsToporDown = false;
	
	//列表底版
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UImage* ListBackGround;

	//列表 (核心)*
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UAwListView* MenuList;

	//设置列表数据
	UFUNCTION()
	void SetEntry(FListItemElementInfo ElementInfos);

	//列表数据选择改变
	UFUNCTION()
	void OnItemSelectionChanged(UObject* Item, bool bIsSelected);

	//设置列表元素
	UFUNCTION()
	void SetListAttributes(EListStyle listStyle,float EntrySpace,UTexture2D* ListBackGroundImage,
		FVector2D ListGroundPosition,FVector2D ListPosition,
		FVector2D ListMaxSize,float ListCom);

	//列表向上选择
	UFUNCTION()
	void ListSelectUp();

	//列表向下选择
	UFUNCTION()
	void ListSelectDown();

	//设置列表状态
	UFUNCTION()
	void SetListState();

	//设置列表大小
	UFUNCTION()
	void SetListSize();

	UFUNCTION()
	FString GetSelectUIName(){return MenuList->GetSelectedItem<UMenuListEntryData>()->Id;}

	UFUNCTION(BlueprintCallable,BlueprintPure)
	UMenuListEntryData* GetSelectData(){return MenuList->GetSelectedItem<UMenuListEntryData>();}


	UFUNCTION(BlueprintCallable,BlueprintPure)
	UMenuListEntry* GetSelectListEntry(){return MenuList->GetEntryWidgetFromItem<UMenuListEntry>(GetSelectData());}
};

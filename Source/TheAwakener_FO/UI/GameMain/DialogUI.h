// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "DialogSelectionItem.h"

#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "Components/CanvasPanel.h"
#include "Components/Image.h"
#include "Components/ListView.h"
#include "Components/TextBlock.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogStructs.h"
#include "UObject/Object.h"
#include "DialogUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UDialogUI : public UBaseUI
{
	GENERATED_BODY()
private:
	// UPROPERTY()
	// UTextBlock* NameText;
	// UPROPERTY()
	// UTextBlock* DialogText;
	UPROPERTY()
	UImage* NextArrow;
	UPROPERTY()
	UCanvasPanel* CursorArea;
	UPROPERTY()
	UCanvasPanel* DialogArea;
	UPROPERTY()
	UCanvasPanel* SelArea;
	UPROPERTY()
	UListView* SelList;

	virtual void NativeOnInitialized() override;
	
	void SelectFirstOne() const;
public:
	UFUNCTION(BlueprintImplementableEvent)
	void SetDialogContent(const FString& Speaker, const FString& Content);
	
	UFUNCTION(BlueprintCallable)
	void HideDialog();
	UFUNCTION(BlueprintCallable)
	void ShowDialog(FString Speaker, FString ShowText);
	UFUNCTION(BlueprintCallable)
	void HideSelections() const;
	UFUNCTION(BlueprintCallable)
	void ShowSelections(TArray<FDialogSelection> Selections) const;
	UFUNCTION(BlueprintCallable)
	void PrevSelection() const;
	UFUNCTION(BlueprintCallable)
	void NextSelection() const;
	UDialogSelectionItem* CurrentSelected() const;
	UFUNCTION(BlueprintCallable)
	void ShowCursor();
	UFUNCTION(BlueprintCallable)
	void HideCursor();

	//对话框是否开着
	UPROPERTY(BlueprintReadOnly)
	bool DialogShowing = false;
	//对话内容是否展示完毕 TODO 目前总是展示完毕的
	UPROPERTY( BlueprintReadOnly)
	bool DialogDoneShown = true;


	UFUNCTION(BlueprintImplementableEvent)
	void PlayHideDialogAnim();
};

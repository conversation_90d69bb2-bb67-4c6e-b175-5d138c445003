// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "MapName.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UMapName : public UBaseUI
{
	GENERATED_BODY()

	UAwGameInstance* GameInstance;


	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UCanvasPanel* AreaCanvasPanel;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* AreaNameTextBlock;
};

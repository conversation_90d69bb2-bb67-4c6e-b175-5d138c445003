// Fill out your copyright notice in the Description page of Project Settings.


#include "IconOnBigMap.h"

void UIconOnBigMap::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	IconImage = Cast<UImage>(GetWidgetFromName("Icon"));
	
}

void UIconOnBigMap::NativeConstruct()
{
	Super::NativeConstruct();
}

void UIconOnBigMap::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

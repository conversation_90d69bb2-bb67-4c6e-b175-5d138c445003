// Fill out your copyright notice in the Description page of Project Settings.


#include "BigMapShade.h"

void UBigMapShade::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	BigMapShadeImage = Cast<UImage>(GetWidgetFromName("BigMapShade"));
}

void UBigMapShade::NativeConstruct()
{
	Super::NativeConstruct();
}

void UBigMapShade::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UBigMapShade::NativeDestruct()
{
	Super::NativeDestruct();
}

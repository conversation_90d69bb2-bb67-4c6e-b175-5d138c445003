// Fill out your copyright notice in the Description page of Project Settings.


#include "GetPlotProps.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UGetPlotProps::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	PlotPropsImage = Cast<UImage>(GetWidgetFromName("PlotProps"));

	PlotPropsTextBlock = Cast<UTextBlock>(GetWidgetFromName("PlotPropsName"));
}

void UGetPlotProps::NativeConstruct()
{
	Super::NativeConstruct();
}

void UGetPlotProps::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UGetPlotProps::SetPlotProps(FString PlotPropsId) const
{
	if(IsValid(PlotPropsImage))
	{
		FString Path = UGameplayFuncLib::GetAwDataManager()->GetItemIconById(PlotPropsId).Path;
		UTexture2D* TempTexture2D = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Path));
		if(IsValid(TempTexture2D))
		{
			PlotPropsImage->SetBrushFromTexture(TempTexture2D);
		}
	}

	if(IsValid(PlotPropsTextBlock))
	{
		PlotPropsTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(PlotPropsId)));
	}
}

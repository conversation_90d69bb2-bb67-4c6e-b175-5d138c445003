// Fill out your copyright notice in the Description page of Project Settings.


#include "BaseText.h"


void UBaseText::PostInitProperties()
{
	Super::PostInitProperties();

	if (AutoLocalizeTextStyle)
	{
		UpdateLocalizationStyle();
	}
}

#if WITH_EDITOR
void UBaseText::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	if (AutoLocalizeTextStyle && PropertyChangedEvent.ChangeType != EPropertyChangeType::Interactive )
	{
		TSharedPtr<SWidget> SafeWidget = GetCachedWidget();
		OrgFontInfo = GetFont();
		if ( SafeWidget.IsValid() )
		{
			UpdateLocalizationStyle();
		}
	}
	
	Super::PostEditChangeProperty(PropertyChangedEvent);
	
}
#endif

void UBaseText::NativeConstruct()
{
		//Bind LanguageChange
		if (AutoLocalizeText||AutoLocalizeTextStyle)
		{
			UAwGameInstance* GameInstance =  UGameplayFuncLib::GetAwGameInstance();
			if (GameInstance)
			{
				CurLanguageType = UGameplayFuncLib::GetCurLanguage();
				GameInstance->LanguageChangeDelegate.AddUniqueDynamic(this,&UBaseText::OnLanguageChange);
				if (AutoLocalizeTextStyle)
				{
					UpdateLocalizationStyle();
				}
			}
		}
		OrgFontInfo = GetFont();
		SetText(GetText());
}


void UBaseText::LocalizeSetText(FText InText)
{
	LocalizeTextKey = InText.ToString();
	SetText(InText);
}

void UBaseText::OnWidgetRebuilt()
{
	Super::OnWidgetRebuilt();
	
	if (!IsDesignTime())
	{
		// Notify the widget that it has been constructed.
		if (!ConstructFlag)
		{
			NativeConstruct();
			ConstructFlag = true;
		}

	}
}

void UBaseText::SetText(FText InText)
{
	if (!AutoLocalizeText)
	{
		Super::SetText(InText);
		return;
	}
	
	if (LocalizeTextKey.IsEmpty())
	{
		Super::SetText(InText);
		return;
	}

	FString NewText ;
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	if (DataManager)
	{
		NewText = DataManager->GetTextByKey(LocalizeTextKey);
	}
	
	Super::SetText(FText::FromString(NewText));
}

void UBaseText::OnLanguageChange()
{
	UAwGameInstance* GameInstance =  UGameplayFuncLib::GetAwGameInstance();
	if (GameInstance)
	{
		CurLanguageType = UGameplayFuncLib::GetCurLanguage();
	}
	
	if (AutoLocalizeText)
	{
		if (OnLanguageTextChangeOverride.IsBound())
		{
			OnLanguageTextChangeOverride.Broadcast();
		}
		else
		{
			SetText(FText::FromString(LocalizeTextKey));
		}
	}
	if (AutoLocalizeTextStyle)
	{
		UpdateLocalizationStyle();
	}
}

void UBaseText::UpdateLocalizationStyle()
{
	if (OnLanguageStyleChangeOverride.IsBound())
	{
		OnLanguageStyleChangeOverride.Broadcast();
		return;
	}
	if (LocalizeStyleData)
	{
		FFontStyleCollection* Data;
		FName RowName = FName(*LocalizeStyleKey);
		FString ContextStr;
		Data = LocalizeStyleData->FindRow<FFontStyleCollection>(RowName,ContextStr);
		if (Data)
		{
			FFontStyleOffset* CurFontStyle = Data->CollectionMap.Find(CurLanguageType);
			if (CurFontStyle)
			{
				if (CurFontStyle->UseFontOffset)
				{
					FSlateFontInfo NewFont = OrgFontInfo;
					NewFont.Size += CurFontStyle->FontOffset.Size;
					NewFont.LetterSpacing += CurFontStyle->FontOffset.LetterSpacing;
					NewFont.SkewAmount += CurFontStyle->FontOffset.SkewAmount;
					NewFont.FontObject = CurFontStyle->FontOffset.FontObject;
					SetFont(NewFont);
				}
				else
				{
					SetFont(CurFontStyle->FontInfo);
				}
				if (!RenderChangeCacheFlag)
				{
					FVector2D NewRenderOffset =    GetRenderTransform().Translation +CurFontStyle->Offset ;
					SetRenderTranslation(NewRenderOffset);
			
					FVector2D NewRenderScale =  FVector2D(GetRenderTransform().Scale.X*CurFontStyle->ScalePower.X,GetRenderTransform().Scale.Y*CurFontStyle->ScalePower.Y);
					SetRenderScale(NewRenderScale);

					RenderChangeCacheFlag = true;
				}
				LineHeightPercentage = CurFontStyle->LineHeightPercentage;
			}
		}
	}
}

void UBaseText::BeginDestroy()
{
	Super::BeginDestroy();

	if (AutoLocalizeText||AutoLocalizeTextStyle)
	{
		UAwGameInstance* GameInstance = UGameplayFuncLib::GetAwGameInstance();
		if (GameInstance)
		{
			GameInstance->LanguageChangeDelegate.RemoveDynamic(this,&UBaseText::OnLanguageChange);
		}
	}
	LocalizeStyleData = nullptr;
	OnLanguageTextChangeOverride.Clear();
	OnLanguageStyleChangeOverride.Clear();
}

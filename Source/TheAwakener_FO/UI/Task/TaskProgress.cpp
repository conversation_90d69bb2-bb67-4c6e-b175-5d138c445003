// Fill out your copyright notice in the Description page of Project Settings.


#include "TaskProgress.h"

void UTaskProgress::NativeOnInitialized()
{
	Super::NativeOnInitialized();
}

void UTaskProgress::NativeConstruct()
{
	Super::NativeConstruct();
}

void UTaskProgress::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UTaskProgress::BeginDestroy()
{
	Super::BeginDestroy();
}

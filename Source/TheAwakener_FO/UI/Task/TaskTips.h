// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "TaskTips.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UTaskTips : public UBaseUI
{
	GENERATED_BODY()

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* TitleTextBlock;

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* TaskNameTextBlock;


	UFUNCTION(BlueprintCallable,BlueprintImplementableEvent)
	void SetTaskTitle(const FString& CompleteTask,const FString& AcceptanceTask,const float& DelayTime = 0);
	
};

// Fill out your copyright notice in the Description page of Project Settings.


#include "TaskTips.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UTaskTips::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	TitleTextBlock = Cast<UTextBlock>(GetWidgetFromName("Title"));

	TaskNameTextBlock = Cast<UTextBlock>(GetWidgetFromName("TaskName"));
}

void UTaskTips::NativeConstruct()
{
	Super::NativeConstruct();
}

void UTaskTips::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}


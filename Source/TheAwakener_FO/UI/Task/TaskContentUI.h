// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "TheAwakener_FO/UI/Base/BaseUI.h"
#include "TaskContentUI.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UTaskContentUI : public UBaseUI
{
	GENERATED_BODY()


	FTimerHandle TimerHandle;

	virtual void NativeOnInitialized() override;
	virtual void NativeConstruct() override;
	virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	UTextBlock* TaskNameTextBlock;

	UFUNCTION(BlueprintCallable)
	void SetTask(FString Task);
	
};

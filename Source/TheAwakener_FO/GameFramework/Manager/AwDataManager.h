// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "BlueprintJsonLibrary.h"
#include "TheAwakener_FO/GameFramework/Input/ActionCmd.h"
#include "TheAwakener_FO/GamePlay/Achievement/Achievement.h"
#include "TheAwakener_FO/GamePlay/AOE/AWAoeBase.h"
#include "TheAwakener_FO/GamePlay/Bullet/AwBullet.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ActionLink.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AIPickActionInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/AI/AwAIComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/ClassModel.h"
#include "TheAwakener_FO/GamePlay/Characters/Creation/NewRoleItem.h"
#include "TheAwakener_FO/GamePlay/Debug/DebugConfig.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogStructs.h"
#include "TheAwakener_FO/GamePlay/Dialog/DialogBubbleComponent.h"
#include "TheAwakener_FO/GamePlay/Elemental/ElementalTalent.h"
#include "TheAwakener_FO/GamePlay/LootSystem/LootPackage.h"
#include "TheAwakener_FO/UI/OldMenu/CharacterInfo/Skill/SkillInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Skill/AwSkill.h"
#include "TheAwakener_FO/GamePlay/Trigger/AwSceneItem.h"
#include "TheAwakener_FO/GamePlay/Trigger/AwTrigger.h"
#include "TheAwakener_FO/GamePlay/Map/AwMapInfo.h"
#include "TheAwakener_FO/GamePlay/Map/AwDungeonInfo.h"
#include "TheAwakener_FO/GamePlay/Item/ItemModel.h"
#include "TheAwakener_FO/GamePlay/Map/AwDungeonRoomInfo.h"
#include "TheAwakener_FO/GamePlay/Relationship/CharacterCamp.h"
#include "TheAwakener_FO/GamePlay/Role/RoleCreation.h"
#include "TheAwakener_FO/GamePlay/Setting/DefaultConfig.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingUIInfo.h"
#include "TheAwakener_FO/GamePlay/Trading/Trading.h"
#include "TheAwakener_FO/GamePlay/Quest/AwQuest.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyle.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleStyle/RogueBattleStyleUpgrade.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgrade.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Relic/AwRelicSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Item/AwRogueItemSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Talent/AwRogueTalentSubSystem.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Map/RogueMapConfig.h"
#include "TheAwakener_FO/GamePlay/Roguelike/Weapon/RogueWeapon.h"
#include "TheAwakener_FO/UI/AwWidgetInfo.h"
#include "TheAwakener_FO/UI/GameMain/GameMain.h"
#include "TheAwakener_FO/UI/GameMain/GameMenu.h"
#include "TheAwakener_FO/UI/GameMain/MenuList.h"
#include "TheAwakener_FO/UI/MapUI/BigMap.h"
#include "TheAwakener_FO/UI/Subtitle/Subtitle.h"
#include "TheAwakener_FO/GameData/MobProp.h"
#include "TheAwakener_FO/GamePlay/DropLoot/DropLoot.h"
#include "TheAwakener_FO/GamePlay/Survivor/CWaveMonsterSpawn.h"
#include "TheAwakener_FO/GamePlay/Survivor/LevelData.h"
#include "AwDataManager.generated.h"

/**
 * 
 */
//json里读取出来的function数据
// USTRUCT(BlueprintType)
// struct FJsonFuncData
// {
// 	GENERATED_BODY()
// public:
// 	//function逻辑实现的类地址
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 		FString ClassPath;
// 	//function的名字
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 		FString FunctionName;
// 	//function的输入参数
// 	UPROPERTY(EditAnywhere, BlueprintReadWrite)
// 		TArray<FString> Params;
// };

USTRUCT()
struct FMapPointsInLevel
{
	GENERATED_BODY()
public:
	TArray<FPresetMapPoint> PointOffsetList;
};

UCLASS(BlueprintType, Blueprintable, config = AWManager)
class THEAWAKENER_FO_API UAwDataManager : public UObject
{
	GENERATED_BODY()
public:
	
	TMap<FString, FConfigFile*> ConfigFiles;
	TMap<FString, FBlueprintJsonObject> JsonObjsMap;
	
	TMap<FString, FBattleClassModel> ClassInfos;
	TMap<FString, TArray<FActionInfo>> BaseActionType;
	// 肉鸽法器动作信息
	TMap<FString, FActionInfo> RogueItemActions;
	TMap<FString, FMobModel> MobModels;
	TMap<FString, FAOEModel> AoeModels;			//all aoe in json
	TMap<FString, FBulletModel> BulletModels;	//all bullet in json
	TMap<FString, FBuffModel> BuffModels;		//all buff in json
	TMap<FString, FActionCmd> ActionCmds = TMap<FString, FActionCmd>();		//all action to cmd in json
	// 能自定义的 Action
	TArray<FCanCustomAction> CanCustomActions;
	// 手柄按键能用的 Key
	TArray<FString> CanUseKey_Gamepad;
	// 键盘按键能用的 Key
	TArray<FString> CanUseKey_Keyboard;
	TMap<FString, FAIScriptPart> AIScripts;		//all AIScript in json
	TMap<FString, FDefaultKeyMapping> DefaultKeyMappings;
	TMap<FString, FSkillInfo> SkillInfos;
	TMap<FString, FSceneItemModel> SceneItemModels;
	TMap<FString, FAwTrigger> TriggerModels;
	TMap<FString, FEquipment> Equipments;
	TMap<FString, FWeaponModel> WeaponModels;
	TMap<FString, FDungeonRoomLevelInfo> DungeonRoomLevels;  //副本可随机的房间地图信息
	TMap<FString, FDungeonRoadModel> DungeonRoadModels;  //副本可随机的通道信息
	TMap<FString, FAwDungeonModel> DungeonInfos;  //各个副本信息
	TMap<FString, FAwMapInfo> MapInfos;  //各个地图信息
	TMap<FString, FLootPackage> LootPackages;
	TMap<FString, FMobDropLootInfo> MobDropLootInfos; // 穆风特供版掉落
	TMap<FString, FItemModel> Items;
	TMap<FString, FTrading> Shops;	//所有的商店信息
	TArray<FThingUIInfo> BaseThingUIInfo;		//基础的物品UI显示信息，这些都是事先配置好的
	TMap<FString, FAwWidgetInfo> WidgetInfos;
	TMap<FString, FString> GameTexts_CN;	//游戏中的文字，<Key, 文字内容>，TODO：目前只读取Chinese的
	TMap<FString, FString> GameTexts_EN;	//TODO：QSL 临时做法，后面需要改成 Map 套 Map
	TMap<FString, FString> GameTexts_KR;
	TMap<FString, FString> DialogueAudios_CN;
	TMap<FString, FString> DialogueAudios_EN;
	TMap<FString, FLanguage> GameText;
	TMap<FString, FElementalTalent> ElementalTalents;
	TMap<FString, FElementalTalentUIInfo> ElementalTalentUIInfos;
	TMap<FString, FActionSelection> ActionSelections;	//主技能替换数据
	TMap<FString, FActionLink> ActionLinks;			//子技能配置数据
	TMap<FString, FActionSelectionUIInfo> ActionSelectionUIInfo;	//主技能替换的ui数据
	TMap<FString, FActionLinkUIInfo> ActionLinkUIInfo;	//子技能配置界面ui
	TMap<uint8, FCharacterCamp> CampInfos;	//默认的阵营之间的关系
	TMap<FString, FDialogScriptModel> DialogModels;	//对话脚本
	TMap<FString, FListItemElementInfo> ListAllItems;
	TMap<FString, FClassIcon> PlayerClassIcons;
	TMap<FString, FItemIcon> ItemIcons;
	TMap<FString, FUIAudio> UIAudios;
	TMap<FString, FString> BgmList;
	TMap<FString, FAIPickActionInfo> AIPickActionInfos;	//AI选择动作的策略数据
	TMap<FString, FRogueAIPickActionInfo> RogueAIPickActionInfos;	//Rogue AI选择动作的策略数据
	TMap<FString, FEquipmentSet> EquipmentSets; //词条
	TMap<FString, FCreateBigMapNeedVariable> CreateBigMapNeedVaribles;
	TMap<FString, FDialogBubbleGroupMobel> DialogBubbleGroups;
	TMap<FString, FSubtitleGroup> SubtitleGroups;
	TMap<FString, FAwQuest> Quests;//所有的任务信息
	TMap<FString, FAwQuestTarget> QuestTargets;//所有任务目标的信息
	TMap<FString, FAwActionSkillInfo> ActionSkillInfos;//所有动作技能的信息
	TMap<FString, FLevelSelectionData> LevelSelections;//所有关卡信息
	TMap<FString, FLevelIntroduce>LevelIntroduces;//所有关卡介绍
	
	//-----------------------幸存者相关---------------------
	TMap<FString,FAwRelicInfo> RelicInfos_Survivor;
	TMap<FString,FAwRogueItemInfo> RogueItemInfos_Survivor;
	// 角色(职业)Id - 角色战斗强化信息 
	TMap<FString, FRogueBattleUpgrade> RogueBattleUpgrades_Survivor;
	//-----------------------肉鸽相关---------------------
	TMap<FString,FAwRelicInfo> RelicInfos;
	TMap<FString,FAwRogueItemInfo> RogueItemInfos;
	TMap<FString,FAwRogueTalentInfo> RogueTalentInfos;
	TMap<int,FRogueRoomStepInfo> RogueRoomStepConfig;
	TMap<int,FRogueRoomStepInfo> RogueClearedRoomStepConfig;
	TMap<FString,FRogueLevelInfo> RogueLevelList;
	TMap<FString,FAwRoomRelicDrop> RogueRoomRelicDrop;
	// 肉鸽战斗风格
	TMap<FString, FRogueBattleStyle> RogueBattleStyle;
	// 肉鸽战斗风格强化
	TMap<FString, TMap<FString, FRogueBattleStyleUpgrade>> RogueBattleStyleUpgrade;
	// 随机算法里用的各个等级的权重分布
	// [1~10关,11~20关,21~31关]
	TArray<FAbilityLevelWeight> AbilityLevelWeights;
	// 3级动作添加的效果buff
	TMap<FString, FActionLv3Buffs> ActionLv3Buffs;
	// 肉鸽角色战斗强化
	// 角色(职业)Id - 角色战斗强化信息 
	TMap<FString, FRogueBattleUpgrade> RogueBattleUpgrades;
	// VideoPath
	TMap<FString, FString> VideoPaths;
	// 肉鸽武器信息
	TMap<FString, FRogueWeaponInfo> RogueWeaponInfos;
	// 肉鸽武器对应的伤害盒
	TMap<FString, FWeaponAtkBox> RogueWeaponAtkBoxes;
	// 每个角色用用的武器Id列表
	TMap<FString, FClassWeapons> ClassWeapons;
	// 肉鸽武器默认信息
	FWeaponDefaultInfo WeaponDefaultInfo;
	//房间出怪Group
	TMap<FString,FRogueRoomWaveGroup> RogueRoomWaveGroups;
	//各个房间的出怪信息
	TArray<FRogueRoomMobInfo> RogueRoomWaveInfos;
	
	// --------- 表数据修改表 ---------
	// 怪物属性
	TMap<FString, FMobProp> MobProps;
	UFUNCTION(BlueprintPure)
	TMap<FString, FMobProp> GetAllMobProp();
	UFUNCTION(BlueprintPure)
	FMobProp GetMobProp(FString Id,FString AlterId);
	
	//-----------------------肉鸽结束--------------------
	//角色创建
	FRoleCreation RoleCreation;
	//新角色给的东西<角色Type, 东西信息>
	TMap<FString, FNewRoleItem> NewRoleItems;
	
	void ReadJsonFile(FString JsonPath);
	FConfigFile* GetConfigFile(const FString& ConfigName);
	//-----------------------关卡信息--------------------
	// 根据类型获取关卡列表
	UFUNCTION(BlueprintCallable,BlueprintPure)
	TArray<FLevelSelectionData> GetLevelsByType(ELevelType Type) const;
	// 根据分组ID获取关卡列表
	UFUNCTION(BlueprintCallable,BlueprintPure)
	TArray<FLevelSelectionData> GetLevelsByGroupID(const FString& GroupID) const;
	// 根据ID查找关卡
	UFUNCTION(BlueprintCallable,BlueprintPure)
	FLevelSelectionData FindLevelByID(const FString& ID);
	UFUNCTION(BlueprintCallable,BlueprintPure)
	FLevelIntroduce FindLevelIntroByID(const FString& ID);
public:
	UAwDataManager();

	virtual void BeginDestroy() override;

	bool  bIsRogueMode = false;
	//测试配置
	UPROPERTY(BlueprintReadOnly)
	FDebugConfig DebugConfig;
	//默认配置
	UPROPERTY(BlueprintReadOnly)
	FDefaultConfig DefaultConfig;
	//关卡阶段配置
	UPROPERTY(BlueprintReadOnly)
	TMap<FString,FCWaveMonsterSpawn> WaveMonsterData;
	UPROPERTY(BlueprintReadOnly)
	TMap<FString,FCWaveEvent> WaveEventData;
	
	UPROPERTY(BlueprintReadOnly)
	bool CanUseDebug = false;
	
	TArray<FAchievementModel> AchievementModels;	//成就模板，成就总得全部加上的，所以不需要Map
	
	 FBlueprintJsonObject ParseJsonPathToBpJsonObj(const FString JsonPath);
	 TSharedPtr<FJsonObject> ParseJsonPathToJsonObj(const FString JsonPath);
	 TSharedPtr<FJsonObject> ParseJsonStrToJsonObj(const FString JsonStr);
	 FString GetJsonDirPath();

	UFUNCTION(BlueprintCallable, Category = "Aw|IniConfig")
	 FString GetConfigValue(const FString& ConfigName, const FString& Section, const FString& Key);

	static FString ConfigValue(const FString& ConfigName, const FString& Section, const FString& Key);
	
	/**
	 * @brief 初始解析指定地址（Content/Json/）所有的.json文件（包含子文件夹的.json文件）存到DataManager
	 * Parse all .json files in the address(Content/Json/) (.Json file containing subfolders) and save them to DataManager.
	 */
	UFUNCTION(BlueprintCallable, Category = "Aw|DataManager")
	void ParseData();

	/**
	 * @brief 打印所有JsonObjsMap
	 * Print all tables in the JsonObjsMap.
	 */
	UFUNCTION(BlueprintCallable, Category = "Aw|DataManager")
	void PrintAllTables();

	/**
	 * @brief 打印某个JsonObj
	 * Print the JsonObj which name is "JsonName".
	 * @param JsonName
	 */
	UFUNCTION(BlueprintCallable, Category = "Aw|DataManager")
	void PrintTable(FString JsonName);

	/**
	 * @brief 从DataManager的json数据data里获取某个表的FBlueprintJsonObject
	 * Get the FBlueprintJsonObject of a table from the JsonObjsMap of the DataManger which name is "JsonName".
	 * @param JsonName
	 * @return
	 */
	UFUNCTION(BlueprintPure, Category = "Aw|DataManager")
	FBlueprintJsonObject GetTable(FString JsonName);

	//FJsonFuncData SplitFuncNameAndParams(FString InString);

	UFUNCTION(BlueprintPure, Category = "Get")
	FAwActionSkillInfo GetActionSkillInfoById(FString Id)
	{
		FAwActionSkillInfo Result = FAwActionSkillInfo();
		if (ActionSkillInfos.Contains( Id))
		{
			Result = ActionSkillInfos[Id];
		}
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString,FAwActionSkillInfo> GetAllAwakeSkillnfo()
	{
		return ActionSkillInfos;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FBattleClassModel GetBattleClassModelById(FString Id)
	{
		FBattleClassModel Result = FBattleClassModel();
		if (ClassInfos.Contains(Id))
			Result = ClassInfos[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FMobModel> GetMobModels() { return this->MobModels; }
	UFUNCTION(BlueprintPure, Category = "Get")
	FMobModel GetMobModelById(FString Id,FString AlterId = "");
	UFUNCTION(BlueprintPure, Category = "Get")
	int GetMobExp(FString Id,FString AlterId = "");
	UFUNCTION(BlueprintPure, Category = "Get")
	FAOEModel GetAoeModelById(FString Id)
	{
		FAOEModel Result = FAOEModel();
		if (AoeModels.Contains(Id))
			Result = AoeModels[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FBulletModel GetBulletModelById(FString Id)
	{
		FBulletModel Result = FBulletModel();
		if (BulletModels.Contains(Id))
			Result = BulletModels[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FBuffModel GetBuffModelById(FString Id)
	{
		if (BuffModels.Contains(Id))
			return BuffModels[Id];
		return FBuffModel();
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FActionCmd GetActionCmdById(FString Id)
	{
		FActionCmd Result = FActionCmd();
		if (ActionCmds.Num()>0 && ActionCmds.Contains(Id))
			Result = ActionCmds[Id];
		return Result;
	}
	
	UFUNCTION(BlueprintPure, Category = "Get")
	TArray<FCanCustomAction> GetCanCustomAction() { return CanCustomActions; }
	UFUNCTION(BlueprintPure, Category = "Get")
	TArray<FString> GetCanUseKey_Gamepad() { return CanUseKey_Gamepad; }
	UFUNCTION(BlueprintPure, Category = "Get")
	TArray<FString> GetCanUseKey_Keyboard() { return CanUseKey_Keyboard; }
	
	UFUNCTION(BlueprintPure, Category = "Get")
	FAIScriptPart GetAIScriptById(FString Id) 
	{
		FAIScriptPart Result = FAIScriptPart();
		if (AIScripts.Contains(Id))
			Result = AIScripts[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FDefaultKeyMapping GetDefaultKeyMappingById(FString Id)
	{
		FDefaultKeyMapping Result = FDefaultKeyMapping();
		if (DefaultKeyMappings.Contains(Id))
			Result = DefaultKeyMappings[Id];
		return Result;
	}
	
	UFUNCTION(BlueprintPure, Category = "Get")
	FDefaultKeyMapping GetKeyMappingById(FString Id);
	
	UFUNCTION(BlueprintPure, Category = "Get")
	TArray<FActionInfo> GetBaseActionsById(FString Id) 
	{
		TArray<FActionInfo> Result;
		if (BaseActionType.Contains(Id))
			Result = BaseActionType[Id];
		return Result;
	}

	// 根据Id返回 法器动作信息
	UFUNCTION(BlueprintPure, Category = "Get")
	FActionInfo GetItemActionById(FString Id)
	{
		if (RogueItemActions.Contains(Id))
			return RogueItemActions[Id];
		return FActionInfo();
	}

	// 获取 所有的法器动作信息
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FActionInfo> GetAllItemActionInfos()
	{
		return RogueItemActions;
	}
	
	UFUNCTION(BlueprintPure, Category = "Get")
	FSkillInfo GetUISkillInfoById(FString Id)
	{
		FSkillInfo Result;
		if (SkillInfos.Contains(Id))
			Result = SkillInfos[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FSceneItemModel GetSceneItemModelById(FString Id)
	{
		FSceneItemModel Result = FSceneItemModel();
		if (SceneItemModels.Contains(Id))
			Result = SceneItemModels[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FAwTrigger GetTriggerModelById(FString Id)
	{
		FAwTrigger Result = FAwTrigger();
		if (TriggerModels.Contains(Id))
			Result = TriggerModels[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TArray<FAwTrigger> GetAllTriggerModel()
	{
		TArray<FAwTrigger> Result;
		for(auto Trigger : TriggerModels)
		{
			Result.Add(Trigger.Value);
		}
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FAwQuest GetQuestlById(FString Id)
	{
		FAwQuest Result = FAwQuest();
		if (Quests.Contains(Id))
			Result = Quests[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FAwQuest> GetAllMetaQuests()
	{
		return Quests;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FAwQuestTarget GetQuestTargetById(FString Id)
	{
		FAwQuestTarget Result = FAwQuestTarget();
		if (QuestTargets.Contains(Id))
			Result = QuestTargets[Id];
		return Result;
	}
	//--------Roguelike---------
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FAwRelicInfo> GetAllMetaRelics()
	{
		if(UAwGameInstance::Instance->isSurvivor)
			return  RelicInfos_Survivor;
		else
			return RelicInfos;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FAwRogueItemInfo> GetAllMetaRogueItems()
	{
		if(UAwGameInstance::Instance->isSurvivor)
			return RogueItemInfos_Survivor;
		else
			return RogueItemInfos;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	 FAwRogueItemInfo GetMetaRogueItem(FString Id)
	{
		FAwRogueItemInfo Res = FAwRogueItemInfo();
		if (RogueItemInfos.Contains(Id))
		{
			Res = RogueItemInfos[Id];
		}
		return Res;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FAwRogueTalentInfo> GetAllMetaRogueTalents()
	{
		return RogueTalentInfos;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<int, FRogueRoomStepInfo> GetRogueRoomStepConfig()
	{
		return RogueRoomStepConfig;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<int, FRogueRoomStepInfo> GetClearedRogueRoomStepConfig()
	{
		return RogueClearedRoomStepConfig;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FRogueLevelInfo> GetRogueLevelInfo()
	{
		return RogueLevelList;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	TMap<FString, FAwRoomRelicDrop> GetRogueRoomRelicDrop()
	{
		return RogueRoomRelicDrop;
	}
	//--------Roguelike End---------
	UFUNCTION(BlueprintPure, Category = "Get")
	FEquipment GetEquipmentById(FString EquipmentId)
	{
		FEquipment Res = FEquipment();
		if (Equipments.Contains(EquipmentId))
			Res = Equipments[EquipmentId];
		return Res;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FWeaponModel GetWeaponModelById(FString WeaponId)
	{
		FWeaponModel Res = FWeaponModel();
		if (WeaponModels.Contains(WeaponId))
			Res = WeaponModels[WeaponId];
		return Res;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FLootPackage GetLootPackageById(FString LootPackageId)
	{
		FLootPackage Res = FLootPackage();
		if (LootPackages.Contains(LootPackageId))
			Res = LootPackages[LootPackageId];
		return Res;
	}
	UFUNCTION(BlueprintPure, Category = "Get",meta=(ToolTip="如果没有AlterId或没有对应的AlterId，返回基础部分"))
	FMobDropLootInfo GetDropLootByMobId(FString Id,FString AlterId);
	UFUNCTION(BlueprintPure, Category = "Get")
	FItemModel GetItemById(FString ItemId)
	{
		FItemModel Res = FItemModel();
		if (Items.Contains(ItemId))
			Res = Items[ItemId];
		return Res;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FTrading GetShopById(FString ShopId)
	{
		FTrading Res = FTrading();
		if (Shops.Contains(ShopId))
			Res = Shops[ShopId];
		return Res;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FAwWidgetInfo GetWidgetInfoById(FString WidgetId)
	{
		FAwWidgetInfo Res = FAwWidgetInfo();
		if (WidgetInfos.Contains(WidgetId))
			Res = WidgetInfos[WidgetId];
		return Res;
	}
	UFUNCTION(BlueprintPure, Category="Get")
	FThingUIInfo GetBaseThingUIInfo(EThingType ThingType, FString ThingId)
	{
		for (FThingUIInfo Info : BaseThingUIInfo)
		{
			if (Info.ThingType == ThingType && Info.ThingId == ThingId)
				return Info;
		} 
		return FThingUIInfo();
	}
	UFUNCTION(BlueprintPure, Category="Get")
	FElementalTalent GetElementalTalentById(FString Id)
	{
		if (ElementalTalents.Contains(Id)) return ElementalTalents[Id];
		return FElementalTalent();
	}
	UFUNCTION(BlueprintPure, Category="Get")
	FElementalTalentUIInfo GetElementalTalentDescById(FString Id)
	{
		if (ElementalTalentUIInfos.Contains(Id)) return ElementalTalentUIInfos[Id];
		return FElementalTalentUIInfo();
	}
	UFUNCTION(BlueprintPure, Category="Get")
	FElementalTalent GetElementalTalentDescForElementalTalent(FElementalTalent Talent)
	{
		const FString Id = Talent.Id;
		if (ElementalTalents.Contains(Id)) return ElementalTalents[Id];
		return FElementalTalent();
	}
	UFUNCTION(BlueprintPure, Category="Get")
	FElementalTalentUIInfo GetElementalTalentForDesc(FElementalTalentUIInfo Desc)
	{
		const  FString Id = Desc.Id;
		if (ElementalTalentUIInfos.Contains(Id)) return ElementalTalentUIInfos[Id];
		return FElementalTalentUIInfo();
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FDungeonRoomLevelInfo GetDungeonLevelInfoByPath(FString LevelPath)
	{
		FDungeonRoomLevelInfo RoomInfo;
		TArray<FString> AllLevelPathList;
		for(auto CurRoomInfo : DungeonRoomLevels)
		{
			if(CurRoomInfo.Key == LevelPath)
				return CurRoomInfo.Value;
		}
		return FDungeonRoomLevelInfo();
	}
	UFUNCTION(BlueprintPure, Category = "Get")
		TArray<FDungeonRoadModel> GetDungeonRoadModelByDungeonTags(TArray<FString> Tags)
	{
		TArray<FDungeonRoadModel> Result;
		TArray<FString> AllRoadPathList;
		DungeonRoadModels.GetKeys(AllRoadPathList);
		for (int i = 0; i < AllRoadPathList.Num(); i++)
		{
			if (!DungeonRoadModels[AllRoadPathList[i]].DungeonTag.Num())
				Result.Add(DungeonRoadModels[AllRoadPathList[i]]);
			else
			{
				for (FString& CurTag : Tags)
				{
					if (DungeonRoadModels[AllRoadPathList[i]].DungeonTag.Contains(CurTag))
					{
						Result.Add(DungeonRoadModels[AllRoadPathList[i]]);
						break;
					}
				}
			}
		}
		return Result;
	}

	UFUNCTION(BlueprintPure, Category = "Get")
		FAwMapInfo GetMapInfoByLevelPath(FString LevelPath)
	{
		FAwMapInfo NewMapInfo = FAwMapInfo();
		if (MapInfos.Contains(LevelPath))
			NewMapInfo = MapInfos[LevelPath];
		return NewMapInfo;
	}

	UFUNCTION(BlueprintPure, Category = "Get")
	FAwDungeonModel GetDungeonModelById(FString DungeonId)
	{
		FAwDungeonModel NewDungeon = FAwDungeonModel();
		if (DungeonInfos.Contains(DungeonId))
			NewDungeon = DungeonInfos[DungeonId];
		return NewDungeon;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FString GetTextByKey(FString Key)
	{
		if (UAwGameInstance::Instance)
		{
			if (UAwGameInstance::Instance->Language == ELanguage::Chinese)
			{
				if (GameTexts_CN.Contains(Key))
					return GameTexts_CN[Key];
			}
			else if (UAwGameInstance::Instance->Language == ELanguage::Korean)
			{
				if (GameTexts_KR.Contains(Key))
					return GameTexts_KR[Key];
			}
			else if (UAwGameInstance::Instance->Language == ELanguage::English)
			{
				if (GameTexts_EN.Contains(Key))
					return GameTexts_EN[Key];
			}
		}
		
		return FString();
	}

	UFUNCTION(BlueprintPure, Category = "Get")
	FString GetDialogueAudioByKey(FString Key)
	{
		if (UAwGameInstance::Instance)
		{
			//目前没有英文语音全部使用中文语音
			//if (UAwGameInstance::Instance->Language =="Chinese")
			{
				if (DialogueAudios_CN.Contains(Key))
					return DialogueAudios_CN[Key];
			}
			// else if (UAwGameInstance::Instance->Language == "English")
			// {
			// 	if (DialogueAudios_EN.Contains(Key))
			// 		return DialogueAudios_EN[Key];
			// }
		}
		return FString();
	}

	FString GetTextByKeyAndLanguageType(FString Key)
	{
		if(GameText.Contains(Key))
		{
			/*if(GameText[Key].Language.Contains())
			{
				return GameText[Key].Language[];
			}*/
		}

		return FString();
	}
	
	UFUNCTION(BlueprintPure, Category = "Get")
	FString GetTerrainText(ETerrainType Terrain)
	{
		FString Key;
		switch (Terrain)
		{
		case ETerrainType::Air: Key = "Terrain_Air"; break;
		case ETerrainType::Dirt: Key = "Terrain_Dirt"; break;
		case ETerrainType::Grass: Key = "Terrain_Grass";break;
		case ETerrainType::Ice: Key = "Terrain_Icy"; break;
		case ETerrainType::Magma: Key = "Terrain_Magma"; break;
		case ETerrainType::Rock: Key = "Terrain_Rock"; break;
		case ETerrainType::Water: Key = "Terrain_Water"; break;
		case ETerrainType::Wood: Key = "Terrain_Wood"; break;
		default:return FString();
		}
		if (GameTexts_CN.Contains(Key)) return GameTexts_CN[Key];
		return FString();
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FString GetWeatherText(EWeatherType Weather)
	{
		FString Key;
		switch (Weather)
		{
		case EWeatherType::Rainy: Key = "Weather_Rainy"; break;
		case EWeatherType::Snowy: Key = "Weather_Snowy"; break;
		case EWeatherType::Sunny: Key = "Weather_Sunny";break;
		case EWeatherType::Volcano: Key = "Weather_Volcano"; break;
		default:return FString();
		}
		if (GameTexts_CN.Contains(Key)) return GameTexts_CN[Key];
		return FString();
	}
	//TODO:目前版本专用功能，拿到所有的属性说明文字，毕竟目前只是显示一下，全部显示一下，还没有真正的属性水晶
	UFUNCTION()
	TArray<FElementalTalentUIInfo> AllElementalTalentInfo()
	{
		TArray<FElementalTalentUIInfo> Res;
		for (const TTuple<FString, FElementalTalentUIInfo> ETInfo : ElementalTalentUIInfos)
		{
			Res.Add(ETInfo.Value);
		}
		Res.Sort([](const FElementalTalentUIInfo& E1, const FElementalTalentUIInfo& E2){
			return E1.Level < E2.Level;
		});
		return Res;
	}

	UFUNCTION(BlueprintPure, Category = "Get")
	FActionSelection GetActionSelectionById(FString Id) 
	{
		FActionSelection Result = FActionSelection();
		if (ActionSelections.Contains(Id))
			Result = ActionSelections[Id];
		return Result;
	}

	UFUNCTION(BlueprintPure, Category = "Get")
	TArray<FActionSelection> GetActionSelectionsByClassId(FString ClassId)
	{
		TArray<FActionSelection> ResTArray;
		for (auto action = ActionSelections.CreateConstIterator();action;++action)
		{
			if(action.Value().ClassId == ClassId)
			{
				ResTArray.Add(action.Value());
			}
		}
		
		return ResTArray;
	}
	
	UFUNCTION(BlueprintPure, Category = "Get")
	FActionLink GetActionLinkById(FString Id) 
	{
		FActionLink Result = FActionLink();
		if (ActionLinks.Contains(Id))
			Result = ActionLinks[Id];
		return Result;
	}

	UFUNCTION(BlueprintPure, Category = "Get")
	FActionLink GetActionLinkByMainActionId(FString MainActionId)
	{
		FActionLink Result = FActionLink();
		for (auto Temp = ActionLinks.CreateConstIterator();Temp;++Temp)
		{
			if(Temp.Value().MainActionId == MainActionId)
			{
				Result = Temp.Value();
				return Result;
			}
		}
		
		return Result;
	}
	
	UFUNCTION(BlueprintPure, Category = "Get")
	FActionSelectionUIInfo GetActionSelectionUIInfo(FString Id) 
	{
		FActionSelectionUIInfo Result = FActionSelectionUIInfo();
		if (ActionSelectionUIInfo.Contains(Id))
			Result = ActionSelectionUIInfo[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FActionLinkUIInfo GetActionLinkUIInfo(FString Id) 
	{
		FActionLinkUIInfo Result = FActionLinkUIInfo();
		if (ActionLinkUIInfo.Contains(Id))
			Result = ActionLinkUIInfo[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
	FDialogScriptModel GetDialogById(FString Id) 
	{
		FDialogScriptModel Result = FDialogScriptModel();
		if (DialogModels.Contains(Id))
			Result = DialogModels[Id];
		return Result;
	}
	UFUNCTION(BlueprintPure, Category = "Get")
		FDialogBubbleGroupMobel GetDialogBubbleGroupById(FString Id)
	{
		FDialogBubbleGroupMobel Result = FDialogBubbleGroupMobel();
		if (DialogBubbleGroups.Contains(Id))
			Result = DialogBubbleGroups[Id];
		return Result;
	}


	UFUNCTION(BlueprintCallable)
	TArray<FCharacterCamp> AllDefaultCamps()
	{
		TArray<FCharacterCamp> Res;
		for (auto CampInfo : this->CampInfos)
		{
			Res.Add(CampInfo.Value);
		}
		return Res;
	}

	UFUNCTION(BlueprintCallable)
	FCharacterCamp GetCampInfoBySide(int Side)
	{
		FCharacterCamp TarCamp = FCharacterCamp();
		for (auto CharacterCamp : this->CampInfos)
		{
			if (CharacterCamp.Value.Sides.Contains(Side))
				TarCamp = CharacterCamp.Value;
		}
		return TarCamp;
	}
	
	UFUNCTION(BlueprintCallable,BlueprintPure)
	FRoleCreation GetRoleCreation() { return RoleCreation; }

	UFUNCTION(BlueprintCallable)
	FRoleType GetRolePawnByTypeId(FString TypeId)
	{
		FRoleType Res = FRoleType();
		for (FRoleType Type : RoleCreation.Types)
			if (Type.Id == TypeId)
			{
				Res = Type;
				break;
			}
		return Res;
	}

	UFUNCTION(BlueprintCallable,BlueprintCallable)
	FRoleType GetRolePawnByClassId(FString ClassId)
	{
		FRoleType Res = FRoleType();
		for (FRoleType RoguePawn : RoleCreation.RoguePawns)
			if (RoguePawn.Id == ClassId)
			{
				Res = RoguePawn;
				break;
			}
		return Res;
	}

	UFUNCTION(BlueprintPure)
	TArray<FString> GetAllRoguePawnsClassId()
	{
		TArray<FString> Res;
		for (FRoleType RoguePawn : RoleCreation.RoguePawns)
			Res.Add(RoguePawn.Id);
		return Res;
	}

	UFUNCTION(BlueprintCallable)
	FNewRoleItem GetNewRoleItemsByCharacterType(FString Type)
	{
		if (NewRoleItems.Contains(Type))
		{
			return NewRoleItems[Type];
		}
		return FNewRoleItem();
	}
	
	UFUNCTION(BlueprintCallable)
	FListItemElementInfo GetListItemsById(FString Id)
	{
		FListItemElementInfo Res = FListItemElementInfo();
		if(ListAllItems.Contains(Id))
		{
			Res = ListAllItems[Id];
		}
		return  Res;
	}

	UFUNCTION(BlueprintCallable)
	FClassIcon GetClassIconById(FString Id)
	{
		FClassIcon Res = FClassIcon();
		if(PlayerClassIcons.Contains(Id))
		{
			Res = PlayerClassIcons[Id];
		}
		return Res;
	}

	
	UFUNCTION(BlueprintCallable,BlueprintPure)
	FItemIcon GetItemIconById(FString Id)
	{
		FItemIcon Res = FItemIcon();
		if(ItemIcons.Contains(Id))
		{
			Res = ItemIcons[Id];
		}
		return Res;
	}
	
	UFUNCTION(BlueprintCallable)
	FUIAudio GetUIAudioById(FString Id)
	{
		FUIAudio Res = FUIAudio();
		if(UIAudios.Contains(Id))
		{
			Res = UIAudios[Id];
		}
		return  Res;
	}

	UFUNCTION()
	FAIPickActionInfo GetAIPickActionInfo(FString Id)
	{
		FAIPickActionInfo Res = FAIPickActionInfo();
		if(AIPickActionInfos.Contains(Id))
		{
			Res = AIPickActionInfos[Id];
		}
		return Res;
	}

	UFUNCTION()
	FRogueAIPickActionInfo GetRogueAIPickActionInfo(FString Id)
	{
		FRogueAIPickActionInfo Res = FRogueAIPickActionInfo();
		if(RogueAIPickActionInfos.Contains(Id))
		{
			Res = RogueAIPickActionInfos[Id];
		}
		return Res;
	}

	UFUNCTION(BlueprintCallable)
	FEquipmentSet GetEquipSetByRandom()
	{
		FEquipmentSet Res = FEquipmentSet();
		const bool HasRes = FMath::RandRange(0.0f, 1.0f) > 0.2;
		if (HasRes)
		{
			const int Random = FMath::RandRange(0, EquipmentSets.Num() - 1);
			int Index = 0;
			for (const TTuple<FString, FEquipmentSet> EquipmentSet : EquipmentSets)
			{
				if (Index == Random)
				{
					Res = EquipmentSet.Value;
					return Res;
				}
				Index++;
			}
		}
		return Res;
	}
	UFUNCTION(BlueprintCallable)
	FEquipmentSet GetEquipSetById(FString Id)
	{
		FEquipmentSet Res = FEquipmentSet();
		if(EquipmentSets.Contains(Id))
		{
			Res = EquipmentSets[Id];
		}
		return Res;
	}

	UFUNCTION(BlueprintCallable)
	FCreateBigMapNeedVariable GetBigMapNeedVariableById(FString Id)
	{
		FCreateBigMapNeedVariable Res = FCreateBigMapNeedVariable();
		if(CreateBigMapNeedVaribles.Contains(Id))
		{
			Res = CreateBigMapNeedVaribles[Id];
		}
		return Res;
	}

	UFUNCTION(BlueprintCallable)
	TMap<FString, FString> GetBgmList() { return BgmList; }

	UFUNCTION(BlueprintCallable)
	FSubtitleGroup GetSubtitleGroup(FString Id)
	{
		FSubtitleGroup Res = FSubtitleGroup();
		if(SubtitleGroups.Contains(Id))
		{

			Res = SubtitleGroups[Id];
		}
		return Res;
	}

	UFUNCTION(BlueprintPure)
	FRogueBattleStyle GetRogueBattleStyle(FString BattleStyleId)
	{
		FRogueBattleStyle Res = FRogueBattleStyle();
		if (RogueBattleStyle.Contains(BattleStyleId))
			Res = RogueBattleStyle[BattleStyleId];
		return Res;
	}

	UFUNCTION(BlueprintPure)
	TArray<FRogueBattleStyle> GetRogueBattleStylesByClassId(FString ClassId)
	{
		TArray<FRogueBattleStyle> Res;
		for (TTuple<FString, FRogueBattleStyle> BattleStyle : RogueBattleStyle)
			if (BattleStyle.Value.Class == ClassId)
				Res.Add(BattleStyle.Value);
		
		return Res;
	}

	UFUNCTION(BlueprintPure)
	TArray<FAbilityLevelWeight> GetAbilityLevelWeight()
	{
		return this->AbilityLevelWeights;
	}
		
	UFUNCTION(BlueprintPure)
	TMap<FString, FRogueBattleStyleUpgrade> GetRogueBattleStyleUpgrades(FString BattleStyleId)
	{
		if (RogueBattleStyleUpgrade.Contains(BattleStyleId)) 
			return RogueBattleStyleUpgrade[BattleStyleId];
		return TMap<FString, FRogueBattleStyleUpgrade>();
	}

	UFUNCTION(BlueprintPure)
	FRogueBattleStyleUpgrade GetRogueBattleStyleUpgrade(FString BattleStyleId, FString UpgradeId)
	{
		if (RogueBattleStyleUpgrade.Contains(BattleStyleId))
			if (RogueBattleStyleUpgrade[BattleStyleId].Contains(UpgradeId))
				return RogueBattleStyleUpgrade[BattleStyleId][UpgradeId];
		return FRogueBattleStyleUpgrade();
	}

	UFUNCTION(BlueprintPure)
	FRogueBattleUpgrade GetRogueBattleUpgrade(FString PawnClassId)
	{
		if(UAwGameInstance::Instance->isSurvivor)
		{
			if (RogueBattleUpgrades.Contains(PawnClassId))
				return RogueBattleUpgrades_Survivor[PawnClassId];
		}
		else
			if (RogueBattleUpgrades.Contains(PawnClassId))
				return RogueBattleUpgrades[PawnClassId];

		return FRogueBattleUpgrade();
	}
	
	UFUNCTION(BlueprintPure)
	FRogueBattleAbilityInfo GetRogueBattleAbilityInfo(FString PawnClassId, FString AbilityId)
	{
		FRogueBattleAbilityInfo Res;
		TMap<FString, FRogueBattleUpgrade>* Upgrades = UAwGameInstance::Instance->isSurvivor? &RogueBattleUpgrades_Survivor: &RogueBattleUpgrades;
		if (Upgrades->Contains(PawnClassId))
			for (const FRogueBattleAbilityInfo& AbilityInfo : Upgrades->Find(PawnClassId)->AbilityInfos)
				if (AbilityInfo.Id == AbilityId)
				{
					Res = AbilityInfo;
					break;
				}
		return Res;
	}
		
	UFUNCTION(BlueprintPure)
	FRogueBattleUpgradeInfo GetRogueBattleUpgradeInfo(FString PawnClassId, FString UpgradeId)
	{
		FRogueBattleUpgradeInfo Res;
		if (RogueBattleUpgrades.Contains(PawnClassId))
			for (FRogueBattleUpgradeInfo UpgradeInfo : RogueBattleUpgrades[PawnClassId].UpgradeInfos)
				if (UpgradeInfo.Id == UpgradeId)
				{
					Res = UpgradeInfo;
					break;
				}
		return Res;
	}

	UFUNCTION(BlueprintPure)
	FString GetVideoPath(FString Id);

	UFUNCTION(BlueprintPure)
	FRogueRoomWaveGroup GetRogueRoomWaveGroupById(FString Id)
	{
		if(RogueRoomWaveGroups.Contains(Id))
		{
			return RogueRoomWaveGroups[Id];
		}
		return FRogueRoomWaveGroup();
	}

	UFUNCTION(BlueprintPure)
	FRogueRoomMobInfo GetRoomMobInfo(FString LevelName, FString RoomName, int CurseLevel)
	{
		TArray<FRogueRoomMobInfo> SameRoomInfos;
		for (FRogueRoomMobInfo RoomWaveInfo : RogueRoomWaveInfos)
		{
			if(RoomWaveInfo.LevelName == LevelName && RoomWaveInfo.RoomId == RoomName)
			{
				if(RoomWaveInfo.CurseLevel.Contains(CurseLevel))
					return RoomWaveInfo;
				SameRoomInfos.Add(RoomWaveInfo);
			}
		}
		if(SameRoomInfos.Num())
			return SameRoomInfos[0];
		
		FRogueRoomMobInfo Res = FRogueRoomMobInfo();
		Res.LevelName = LevelName;
		Res.RoomId = RoomName;
		Res.CurseLevel.Add(CurseLevel);
		return Res;
	}
	
	// --------- rogue weapons ---------
	UFUNCTION(BlueprintPure)
	TMap<FString, FRogueWeaponInfo> GetAllRogueWeaponInfo();

	UFUNCTION(BlueprintPure)
	FRogueWeaponInfo GetRogueWeaponInfo(FString RogueWeaponInfoId);
	
	UFUNCTION(BlueprintPure)
	TMap<FString, FClassWeapons> GetAllRogueClassWeapons();

	UFUNCTION(BlueprintPure)
	FClassWeapons GetRogueClassWeapons(FString ClassId);
	
	UFUNCTION(BlueprintPure)
	TMap<FString, FWeaponAtkBox> GetAllRogueWeaponAtkBox();

	UFUNCTION(BlueprintPure)
	FWeaponAtkBox GetWeaponAtkBoxes(const FString& WeaponId);

	UFUNCTION(BlueprintPure)
	FWeaponDefaultInfo GetWeaponDefaultInfo();

	UFUNCTION(BlueprintPure)
	TMap<FString, int> GetWeaponDefaultInfo_UnlockWeapon();

	UFUNCTION(BlueprintPure)
	TMap<FString, FString> GetWeaponDefaultInfo_CurrWeapon();

	UFUNCTION(BlueprintPure)
	FDefaultConfig GetDefaultConfig();
	// --------- --------- ---------
};
